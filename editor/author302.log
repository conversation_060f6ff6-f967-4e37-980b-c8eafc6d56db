This is pdfTeX, Version 3.141592653-2.6-1.40.27 (TeX Live 2025) (preloaded format=pdflatex 2025.3.22)  31 MAY 2025 00:10
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**/Users/<USER>/Documents/GitHub/cai-fm4healthcare-workshop/editor/author302.tex
(/Users/<USER>/Documents/GitHub/cai-fm4healthcare-workshop/editor/author302.tex
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
(/usr/local/texlive/2025/texmf-dist/tex/latex/ieeetran/IEEEtran.cls
Document Class: IEEEtran 2015/08/26 V1.8b by <PERSON>
-- See the "IEEEtran_HOWTO" manual for usage information.
-- http://www.michaelshell.org/tex/ieeetran/
\@IEEEtrantmpdimenA=\dimen141
\@IEEEtrantmpdimenB=\dimen142
\@IEEEtrantmpdimenC=\dimen143
\@IEEEtrantmpcountA=\count196
\@IEEEtrantmpcountB=\count197
\@IEEEtrantmpcountC=\count198
\@IEEEtrantmptoksA=\toks17
LaTeX Font Info:    Trying to load font information for OT1+ptm on input line 503.
(/usr/local/texlive/2025/texmf-dist/tex/latex/psnfss/ot1ptm.fd
File: ot1ptm.fd 2001/06/04 font definitions for OT1/ptm.
)
-- Using 8.5in x 11in (letter) paper.
-- Using PDF output.
\@IEEEnormalsizeunitybaselineskip=\dimen144
-- This is a 10 point document.
\CLASSINFOnormalsizebaselineskip=\dimen145
\CLASSINFOnormalsizeunitybaselineskip=\dimen146
\IEEEnormaljot=\dimen147
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <5> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <5> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <7> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <7> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <8> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <8> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <9> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <9> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <10> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <10> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <11> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <11> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <12> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <12> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <17> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <17> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <20> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <20> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <24> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <24> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.
\IEEEquantizedlength=\dimen148
\IEEEquantizedlengthdiff=\dimen149
\IEEEquantizedtextheightdiff=\dimen150
\IEEEilabelindentA=\dimen151
\IEEEilabelindentB=\dimen152
\IEEEilabelindent=\dimen153
\IEEEelabelindent=\dimen154
\IEEEdlabelindent=\dimen155
\IEEElabelindent=\dimen156
\IEEEiednormlabelsep=\dimen157
\IEEEiedmathlabelsep=\dimen158
\IEEEiedtopsep=\skip49
\c@section=\count199
\c@subsection=\count266
\c@subsubsection=\count267
\c@paragraph=\count268
\c@IEEEsubequation=\count269
\abovecaptionskip=\skip50
\belowcaptionskip=\skip51
\c@figure=\count270
\c@table=\count271
\@IEEEeqnnumcols=\count272
\@IEEEeqncolcnt=\count273
\@IEEEsubeqnnumrollback=\count274
\@IEEEquantizeheightA=\dimen159
\@IEEEquantizeheightB=\dimen160
\@IEEEquantizeheightC=\dimen161
\@IEEEquantizeprevdepth=\dimen162
\@IEEEquantizemultiple=\count275
\@IEEEquantizeboxA=\box52
\@IEEEtmpitemindent=\dimen163
\IEEEPARstartletwidth=\dimen164
\c@IEEEbiography=\count276
\@IEEEtranrubishbin=\box53
) (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks18
) (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
))
\Gin@req@height=\dimen165
\Gin@req@width=\dimen166
) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2024/11/05 v2.17t AMS math features
\@mathmargin=\skip52

For additional information on amsmath, use the `?' option.
(/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks19
\ex@=\dimen167
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen168
) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count277
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count278
\leftroot@=\count279
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count280
\DOTSCASE@=\count281
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box54
\strutbox@=\box55
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen169
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count282
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count283
\dotsspace@=\muskip17
\c@parentequation=\count284
\dspbrk@lvl=\count285
\tag@help=\toks20
\row@=\count286
\column@=\count287
\maxfields@=\count288
\andhelp@=\toks21
\eqnshift@=\dimen170
\alignsep@=\dimen171
\tagshift@=\dimen172
\tagwidth@=\dimen173
\totwidth@=\dimen174
\lineht@=\dimen175
\@envbody=\toks22
\multlinegap=\skip53
\multlinetaggap=\skip54
\mathdisplay@stack=\toks23
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/caption/subcaption.sty
Package: subcaption 2023/07/28 v1.6b Sub-captions (AR)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/caption/caption.sty
Package: caption 2023/08/05 v3.6o Customizing captions (AR)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/caption/caption3.sty
Package: caption3 2023/07/31 v2.4d caption3 kernel (AR)
\caption@tempdima=\dimen176
\captionmargin=\dimen177
\caption@leftmargin=\dimen178
\caption@rightmargin=\dimen179
\caption@width=\dimen180
\caption@indent=\dimen181
\caption@parindent=\dimen182
\caption@hangindent=\dimen183
Package caption Info: Unknown document class (or package),
(caption)             standard defaults will be used.
Package caption Info: \@makecaption = \long macro:#1#2->\ifx \@captype \@IEEEtablestring \footnotesize \bgroup \par \centering \@IEEEtabletopskipstrut {\normalfont \footnotesize #1}\\{\normalfont \footnotesize \scshape #2}\par \addvspace {0.5\baselineskip }\egroup \@IEEEtablecaptionsepspace \else \@IEEEfigurecaptionsepspace \setbox \@tempboxa \hbox {\normalfont \footnotesize {#1.}\nobreakspace \nobreakspace #2}\ifdim \wd \@tempboxa >\hsize \setbox \@tempboxa \hbox {\normalfont \footnotesize {#1.}\nobreakspace \nobreakspace }\parbox [t]{\hsize }{\normalfont \footnotesize \noindent \unhbox \@tempboxa #2}\else \ifCLASSOPTIONconference \hbox to\hsize {\normalfont \footnotesize \hfil \box \@tempboxa \hfil }\else \hbox to\hsize {\normalfont \footnotesize \box \@tempboxa \hfil }\fi \fi \fi  on input line 1175.
)

Package caption Warning: Unknown document class (or package),
(caption)                standard defaults will be used.
See the caption package documentation for explanation.

\c@caption@flags=\count289
\c@continuedfloat=\count290
)
Package caption Info: New subtype `subfigure' on input line 238.
\c@subfigure=\count291
Package caption Info: New subtype `subtable' on input line 238.
\c@subtable=\count292
) (/usr/local/texlive/2025/texmf-dist/tex/latex/multirow/multirow.sty
Package: multirow 2024/11/12 v2.9 Span multiple rows of a table
\multirow@colwidth=\skip55
\multirow@cntb=\count293
\multirow@dima=\skip56
\bigstrutjot=\dimen184
) (/usr/local/texlive/2025/texmf-dist/tex/latex/booktabs/booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen185
\lightrulewidth=\dimen186
\cmidrulewidth=\dimen187
\belowrulesep=\dimen188
\belowbottomsep=\dimen189
\aboverulesep=\dimen190
\abovetopsep=\dimen191
\cmidrulesep=\dimen192
\cmidrulekern=\dimen193
\defaultaddspace=\dimen194
\@cmidla=\count294
\@cmidlb=\count295
\@aboverulesep=\dimen195
\@belowrulesep=\dimen196
\@thisruleclass=\count296
\@lastruleclass=\count297
\@thisrulewidth=\dimen197
)
** ATTENTION: Overriding command lockouts (line 10).
(/usr/local/texlive/2025/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2024-05-08 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count298
\l__pdf_internal_box=\box56
) (./author302.aux)
\openout1 = `author302.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 12.
LaTeX Font Info:    ... okay on input line 12.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 12.
LaTeX Font Info:    ... okay on input line 12.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 12.
LaTeX Font Info:    ... okay on input line 12.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 12.
LaTeX Font Info:    ... okay on input line 12.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 12.
LaTeX Font Info:    ... okay on input line 12.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 12.
LaTeX Font Info:    ... okay on input line 12.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 12.
LaTeX Font Info:    ... okay on input line 12.

-- Lines per column: 56 (exact).
(/usr/local/texlive/2025/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count299
\scratchdimen=\dimen198
\scratchbox=\box57
\nofMPsegments=\count300
\nofMParguments=\count301
\everyMPshowfont=\toks24
\MPscratchCnt=\count302
\MPscratchDim=\dimen199
\MPnumerator=\count303
\makeMPintoPDFobject=\count304
\everyMPtoPDFconversion=\toks25
) (/usr/local/texlive/2025/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 485.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Live
))
Package caption Info: Begin \AtBeginDocument code.
Package caption Info: End \AtBeginDocument code.


<fig302/Fig. 1. The time relationship chart between ECG and PPG.png, id=1, 699.048pt x 530.418pt>
File: fig302/Fig. 1. The time relationship chart between ECG and PPG.png Graphic file (type png)
<use fig302/Fig. 1. The time relationship chart between ECG and PPG.png>
Package pdftex.def Info: fig302/Fig. 1. The time relationship chart between ECG and PPG.png  used on input line 47.
(pdftex.def)             Requested size: 226.79846pt x 172.08434pt.


LaTeX Font Info:    Trying to load font information for TS1+ptm on input line 58.
(/usr/local/texlive/2025/texmf-dist/tex/latex/psnfss/ts1ptm.fd
File: ts1ptm.fd 2001/06/04 font definitions for TS1/ptm.
) [1{/usr/local/texlive/2025/texmf-var/fonts/map/pdftex/updmap/pdftex.map}{/usr/local/texlive/2025/texmf-dist/fonts/enc/dvips/base/8r.enc}


 <./fig302/Fig. 1. The time relationship chart between ECG and PPG.png>]
<fig302/Fig. 2. The multi-modal model architecture for predicting AF.png, id=13, 5437.31375pt x 2476.25125pt>
File: fig302/Fig. 2. The multi-modal model architecture for predicting AF.png Graphic file (type png)
<use fig302/Fig. 2. The multi-modal model architecture for predicting AF.png>
Package pdftex.def Info: fig302/Fig. 2. The multi-modal model architecture for predicting AF.png  used on input line 61.
(pdftex.def)             Requested size: 464.39685pt x 211.47986pt.
LaTeX Font Info:    Trying to load font information for U+msa on input line 76.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 76.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
Underfull \hbox (badness 10000) in paragraph at lines 80--80
[]\OT1/ptm/m/n/8 MIMIC
 []


Underfull \hbox (badness 10000) in paragraph at lines 80--80
\OT1/ptm/m/n/8 PER-form
 []


Underfull \hbox (badness 1552) in paragraph at lines 80--80
[]\OT1/ptm/m/n/8 Bedside mon-i-tor
 []


Underfull \hbox (badness 10000) in paragraph at lines 80--81
[]\OT1/ptm/m/n/8 AF: 29,592
 []


Underfull \hbox (badness 10000) in paragraph at lines 80--81
\OT1/ptm/m/n/8 Non-AF:
 []


Overfull \hbox (58.3843pt too wide) in paragraph at lines 76--83
 [][] 
 []



<fig302/Fig. 3. (a) ECG.png, id=14, 3308.36pt x 2484.28125pt>
File: fig302/Fig. 3. (a) ECG.png Graphic file (type png)
<use fig302/Fig. 3. (a) ECG.png>
Package pdftex.def Info: fig302/Fig. 3. (a) ECG.png  used on input line 124.
(pdftex.def)             Requested size: 123.48138pt x 92.7206pt.
<fig302/Fig. 3. (b) PPG.png, id=15, 1927.2pt x 1445.4pt>
File: fig302/Fig. 3. (b) PPG.png Graphic file (type png)
<use fig302/Fig. 3. (b) PPG.png>
Package pdftex.def Info: fig302/Fig. 3. (b) PPG.png  used on input line 130.
(pdftex.def)             Requested size: 123.48138pt x 92.60893pt.

Underfull \hbox (badness 1377) in paragraph at lines 142--142
[]\OT1/ptm/m/n/10 TABLE III: |THE DE-TAILED PA-RAM-E-TERS OF SIG-
 []

<fig302/Fig. 4 Sig-CLNet architecture diagram.png, id=16, 4456.65pt x 1449.415pt>
File: fig302/Fig. 4 Sig-CLNet architecture diagram.png Graphic file (type png)
<use fig302/Fig. 4 Sig-CLNet architecture diagram.png>
Package pdftex.def Info: fig302/Fig. 4 Sig-CLNet architecture diagram.png  used on input line 157.
(pdftex.def)             Requested size: 252.0pt x 81.94075pt.


[2]

<fig302/Fig. 5. (a) A2CA4C Detection.png, id=20, 2484.28125pt x 2484.28125pt>
File: fig302/Fig. 5. (a) A2CA4C Detection.png Graphic file (type png)
<use fig302/Fig. 5. (a) A2CA4C Detection.png>
Package pdftex.def Info: fig302/Fig. 5. (a) A2CA4C Detection.png  used on input line 184.
(pdftex.def)             Requested size: 80.64185pt x 80.62825pt.
<fig302/Fig. 5. (b) LALV Segmentation.png, id=21, 2484.28125pt x 2484.28125pt>
File: fig302/Fig. 5. (b) LALV Segmentation.png Graphic file (type png)
<use fig302/Fig. 5. (b) LALV Segmentation.png>
Package pdftex.def Info: fig302/Fig. 5. (b) LALV Segmentation.png  used on input line 191.
(pdftex.def)             Requested size: 80.64185pt x 80.62825pt.
<fig302/Fig. 5. (c) RV Segmentation.png, id=22, 2484.28125pt x 2484.28125pt>
File: fig302/Fig. 5. (c) RV Segmentation.png Graphic file (type png)
<use fig302/Fig. 5. (c) RV Segmentation.png>
Package pdftex.def Info: fig302/Fig. 5. (c) RV Segmentation.png  used on input line 198.
(pdftex.def)             Requested size: 80.64185pt x 80.62825pt.
<fig302/Fig. 6. (a) LA Segmentation.jpg, id=23, 642.4pt x 642.4pt>
File: fig302/Fig. 6. (a) LA Segmentation.jpg Graphic file (type jpg)
<use fig302/Fig. 6. (a) LA Segmentation.jpg>
Package pdftex.def Info: fig302/Fig. 6. (a) LA Segmentation.jpg  used on input line 210.
(pdftex.def)             Requested size: 80.64185pt x 80.63307pt.
<fig302/Fig. 6. (b) LV Segmentation.jpg, id=24, 642.4pt x 642.4pt>
File: fig302/Fig. 6. (b) LV Segmentation.jpg Graphic file (type jpg)
<use fig302/Fig. 6. (b) LV Segmentation.jpg>
Package pdftex.def Info: fig302/Fig. 6. (b) LV Segmentation.jpg  used on input line 217.
(pdftex.def)             Requested size: 80.64185pt x 80.63307pt.
<fig302/Fig. 6. (c) RV Segmentation.jpg, id=25, 642.4pt x 642.4pt>
File: fig302/Fig. 6. (c) RV Segmentation.jpg Graphic file (type jpg)
<use fig302/Fig. 6. (c) RV Segmentation.jpg>
Package pdftex.def Info: fig302/Fig. 6. (c) RV Segmentation.jpg  used on input line 224.
(pdftex.def)             Requested size: 80.64185pt x 80.63307pt.
<fig302/Fig. 7. (a) Area.png, id=26, 617.30624pt x 462.72874pt>
File: fig302/Fig. 7. (a) Area.png Graphic file (type png)
<use fig302/Fig. 7. (a) Area.png>
Package pdftex.def Info: fig302/Fig. 7. (a) Area.png  used on input line 235.
(pdftex.def)             Requested size: 123.48138pt x 92.55823pt.
<fig302/Fig. 7. (b) Length.png, id=27, 617.30624pt x 462.72874pt>
File: fig302/Fig. 7. (b) Length.png Graphic file (type png)
<use fig302/Fig. 7. (b) Length.png>
Package pdftex.def Info: fig302/Fig. 7. (b) Length.png  used on input line 240.
(pdftex.def)             Requested size: 123.48138pt x 92.55823pt.
<fig302/Fig. 7. (c) Volume.png, id=28, 617.30624pt x 462.72874pt>
File: fig302/Fig. 7. (c) Volume.png Graphic file (type png)
<use fig302/Fig. 7. (c) Volume.png>
Package pdftex.def Info: fig302/Fig. 7. (c) Volume.png  used on input line 246.
(pdftex.def)             Requested size: 123.48138pt x 92.55823pt.
<fig302/Fig. 7. (d) Width.png, id=29, 617.30624pt x 462.72874pt>
File: fig302/Fig. 7. (d) Width.png Graphic file (type png)
<use fig302/Fig. 7. (d) Width.png>
Package pdftex.def Info: fig302/Fig. 7. (d) Width.png  used on input line 251.
(pdftex.def)             Requested size: 123.48138pt x 92.55823pt.
<fig302/Fig. 8. Ultra-CNet architecture diagram.png, id=30, 4227.795pt x 1593.955pt>
File: fig302/Fig. 8. Ultra-CNet architecture diagram.png Graphic file (type png)
<use fig302/Fig. 8. Ultra-CNet architecture diagram.png>
Package pdftex.def Info: fig302/Fig. 8. Ultra-CNet architecture diagram.png  used on input line 278.
(pdftex.def)             Requested size: 252.0pt x 95.00081pt.


[3 <./fig302/Fig. 2. The multi-modal model architecture for predicting AF.png> <./fig302/Fig. 3. (a) ECG.png> <./fig302/Fig. 3. (b) PPG.png> <./fig302/Fig. 4 Sig-CLNet architecture diagram.png>]

LaTeX Warning: Text page 4 contains only floats.



<fig302/Fig. 9. The final MLP architecture diagram.png, id=39, 2750.275pt x 2128.95375pt>
File: fig302/Fig. 9. The final MLP architecture diagram.png Graphic file (type png)
<use fig302/Fig. 9. The final MLP architecture diagram.png>
Package pdftex.def Info: fig302/Fig. 9. The final MLP architecture diagram.png  used on input line 305.
(pdftex.def)             Requested size: 252.0pt x 195.041pt.


[4 <./fig302/Fig. 5. (a) A2CA4C Detection.png> <./fig302/Fig. 5. (b) LALV Segmentation.png> <./fig302/Fig. 5. (c) RV Segmentation.png> <./fig302/Fig. 6. (a) LA Segmentation.jpg> <./fig302/Fig. 6. (b) LV Segmentation.jpg> <./fig302/Fig. 6. (c) RV Segmentation.jpg> <./fig302/Fig. 7. (a) Area.png> <./fig302/Fig. 7. (b) Length.png> <./fig302/Fig. 7. (c) Volume.png> <./fig302/Fig. 7. (d) Width.png> <./fig302/Fig. 8. Ultra-CNet architecture diagram.png>]


Underfull \hbox (badness 5331) in paragraph at lines 418--418
[]\OT1/ptm/m/n/10 TABLE VIII: |THE MIS-CLAS-SI-FI-CA-TION RATE OF
 []



[5 <./fig302/Fig. 9. The final MLP architecture diagram.png>]
Overfull \hbox (7.87016pt too wide) in paragraph at lines 456--466
 [][] 
 []


Underfull \hbox (badness 6876) in paragraph at lines 487--487
[]\OT1/ptm/m/n/8 Risk As-sess-ment for Is-chemic
 []


Underfull \hbox (badness 7362) in paragraph at lines 487--487
[]\OT1/ptm/m/n/8 AUROC: 0.86 AUCPR
 []

<fig302/Fig. 10. (a) Sig-CLNet accuracy.png, id=57, 617.30624pt x 462.72874pt>
File: fig302/Fig. 10. (a) Sig-CLNet accuracy.png Graphic file (type png)
<use fig302/Fig. 10. (a) Sig-CLNet accuracy.png>
Package pdftex.def Info: fig302/Fig. 10. (a) Sig-CLNet accuracy.png  used on input line 496.
(pdftex.def)             Requested size: 247.6778pt x 185.65306pt.
<fig302/Fig. 10. (b) Sig-CLNet loss.png, id=58, 617.30624pt x 462.72874pt>
File: fig302/Fig. 10. (b) Sig-CLNet loss.png Graphic file (type png)
<use fig302/Fig. 10. (b) Sig-CLNet loss.png>
Package pdftex.def Info: fig302/Fig. 10. (b) Sig-CLNet loss.png  used on input line 502.
(pdftex.def)             Requested size: 247.6778pt x 185.65306pt.
<fig302/Fig. 10. (c) Ultra-CNet accuracy.png, id=59, 617.30624pt x 462.72874pt>
File: fig302/Fig. 10. (c) Ultra-CNet accuracy.png Graphic file (type png)
<use fig302/Fig. 10. (c) Ultra-CNet accuracy.png>
Package pdftex.def Info: fig302/Fig. 10. (c) Ultra-CNet accuracy.png  used on input line 508.
(pdftex.def)             Requested size: 247.6778pt x 185.65306pt.
<fig302/Fig. 10. (d) Ultra-CNet loss.png, id=60, 617.30624pt x 462.72874pt>
File: fig302/Fig. 10. (d) Ultra-CNet loss.png Graphic file (type png)
<use fig302/Fig. 10. (d) Ultra-CNet loss.png>
Package pdftex.def Info: fig302/Fig. 10. (d) Ultra-CNet loss.png  used on input line 514.
(pdftex.def)             Requested size: 247.6778pt x 185.65306pt.
<fig302/Fig. 10. (e) The final MLP classifier accuracy.png, id=61, 617.30624pt x 462.72874pt>
File: fig302/Fig. 10. (e) The final MLP classifier accuracy.png Graphic file (type png)
<use fig302/Fig. 10. (e) The final MLP classifier accuracy.png>
Package pdftex.def Info: fig302/Fig. 10. (e) The final MLP classifier accuracy.png  used on input line 520.
(pdftex.def)             Requested size: 247.6778pt x 185.65306pt.
<fig302/Fig. 10. (f) The final MLP classifier loss.png, id=62, 617.30624pt x 462.72874pt>
File: fig302/Fig. 10. (f) The final MLP classifier loss.png Graphic file (type png)
<use fig302/Fig. 10. (f) The final MLP classifier loss.png>
Package pdftex.def Info: fig302/Fig. 10. (f) The final MLP classifier loss.png  used on input line 526.
(pdftex.def)             Requested size: 247.6778pt x 185.65306pt.


<fig302/Fig. 11. (a) Sig-CLNet confusion matrix.png, id=63, 617.30624pt x 462.72874pt>
File: fig302/Fig. 11. (a) Sig-CLNet confusion matrix.png Graphic file (type png)
<use fig302/Fig. 11. (a) Sig-CLNet confusion matrix.png>
Package pdftex.def Info: fig302/Fig. 11. (a) Sig-CLNet confusion matrix.png  used on input line 538.
(pdftex.def)             Requested size: 80.64185pt x 60.44633pt.
<fig302/Fig. 11. (b) Ultra-CNet confusion matrix.png, id=64, 617.30624pt x 462.72874pt>
File: fig302/Fig. 11. (b) Ultra-CNet confusion matrix.png Graphic file (type png)
<use fig302/Fig. 11. (b) Ultra-CNet confusion matrix.png>
Package pdftex.def Info: fig302/Fig. 11. (b) Ultra-CNet confusion matrix.png  used on input line 544.
(pdftex.def)             Requested size: 80.64185pt x 60.44633pt.
<fig302/Fig. 11. (c) The final MLP classifier confusion matrix.png, id=65, 617.30624pt x 462.72874pt>
File: fig302/Fig. 11. (c) The final MLP classifier confusion matrix.png Graphic file (type png)
<use fig302/Fig. 11. (c) The final MLP classifier confusion matrix.png>
Package pdftex.def Info: fig302/Fig. 11. (c) The final MLP classifier confusion matrix.png  used on input line 550.
(pdftex.def)             Requested size: 80.64185pt x 60.44633pt.

Underfull \hbox (badness 3078) in paragraph at lines 551--551
[]\OT1/ptm/m/n/9 (c) |The fi-nal MLP
 []

(./references302.tex

[6])

** Conference Paper **
Before submitting the final camera ready copy, remember to:

 1. Manually equalize the lengths of two columns on the last page
 of your paper;

 2. Ensure that any PostScript and/or PDF output post-processing
 uses only Type 1 fonts and that every step in the generation
 process uses the appropriate paper size.





[7

]

LaTeX Warning: Text page 8 contains only floats.


Overfull \vbox (5.15926pt too high) has occurred while \output is active []




LaTeX Warning: Text page 8 contains only floats.


Overfull \vbox (5.15926pt too high) has occurred while \output is active []



[8 <./fig302/Fig. 10. (a) Sig-CLNet accuracy.png> <./fig302/Fig. 10. (b) Sig-CLNet loss.png> <./fig302/Fig. 10. (c) Ultra-CNet accuracy.png> <./fig302/Fig. 10. (d) Ultra-CNet loss.png> <./fig302/Fig. 10. (e) The final MLP classifier accuracy.png> <./fig302/Fig. 10. (f) The final MLP classifier loss.png>]



[9

 <./fig302/Fig. 11. (a) Sig-CLNet confusion matrix.png> <./fig302/Fig. 11. (b) Ultra-CNet confusion matrix.png> <./fig302/Fig. 11. (c) The final MLP classifier confusion matrix.png>] (./author302.aux)
 ***********
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
 ***********
 ) 
Here is how much of TeX's memory you used:
 4873 strings out of 473190
 90812 string characters out of 5715800
 499451 words of memory out of 5000000
 28092 multiletter control sequences out of 15000+600000
 596638 words of font info for 108 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 67i,11n,77p,1196b,469s stack positions out of 10000i,1000n,20000p,200000b,200000s
</usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi10.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmr10.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmr7.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy10.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/symbols/msbm10.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/urw/times/utmb8a.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/urw/times/utmbi8a.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/urw/times/utmr8a.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/urw/times/utmri8a.pfb>
Output written on author302.pdf (9 pages, 12245461 bytes).
PDF statistics:
 128 PDF objects out of 1000 (max. 8388607)
 51 compressed objects within 1 object stream
 0 named destinations out of 1000 (max. 500000)
 131 words of extra memory for PDF output out of 10000 (max. 10000000)

