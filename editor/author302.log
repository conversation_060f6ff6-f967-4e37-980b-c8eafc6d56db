This is pdfTeX, Version 3.141592653-2.6-1.40.27 (TeX Live 2025) (preloaded format=pdflatex 2025.3.22)  31 MAY 2025 00:37
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**/Users/<USER>/Documents/GitHub/cai-fm4healthcare-workshop/editor/author302.tex
(/Users/<USER>/Documents/GitHub/cai-fm4healthcare-workshop/editor/author302.tex
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
(./svmult.cls
Document Class: svmult 2024/03/15 v5.11 
Springer Verlag global LaTeX document class for multi authored books
Class Springer-SVMult Info: extra/valid Springer sub-package 
(Springer-SVMult)           not found in option list - using "global" style.
(/usr/local/texlive/2025/texmf-dist/tex/latex/base/article.cls
Document Class: article 2024/06/29 v1.4n Standard LaTeX document class
(/usr/local/texlive/2025/texmf-dist/tex/latex/base/size10.clo
File: size10.clo 2024/06/29 v1.4n Standard LaTeX file (size option)
)
\c@part=\count196
\c@section=\count197
\c@subsection=\count198
\c@subsubsection=\count199
\c@paragraph=\count266
\c@subparagraph=\count267
\c@figure=\count268
\c@table=\count269
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
)
\svparindent=\dimen142
\bibindent=\dimen143
\betweenumberspace=\dimen144
\headlineindent=\dimen145
\minitoc=\write3
\c@minitocdepth=\count270
\c@chapter=\count271
\mottowidth=\dimen146
\svitemindent=\dimen147
\verbatimindent=\dimen148
LaTeX Font Info:    Redeclaring math symbol \Gamma on input line 975.
LaTeX Font Info:    Redeclaring math symbol \Delta on input line 976.
LaTeX Font Info:    Redeclaring math symbol \Theta on input line 977.
LaTeX Font Info:    Redeclaring math symbol \Lambda on input line 978.
LaTeX Font Info:    Redeclaring math symbol \Xi on input line 979.
LaTeX Font Info:    Redeclaring math symbol \Pi on input line 980.
LaTeX Font Info:    Redeclaring math symbol \Sigma on input line 981.
LaTeX Font Info:    Redeclaring math symbol \Upsilon on input line 982.
LaTeX Font Info:    Redeclaring math symbol \Phi on input line 983.
LaTeX Font Info:    Redeclaring math symbol \Psi on input line 984.
LaTeX Font Info:    Redeclaring math symbol \Omega on input line 985.
\tocchpnum=\dimen149
\tocsecnum=\dimen150
\tocsectotal=\dimen151
\tocsubsecnum=\dimen152
\tocsubsectotal=\dimen153
\tocsubsubsecnum=\dimen154
\tocsubsubsectotal=\dimen155
\tocparanum=\dimen156
\tocparatotal=\dimen157
\tocsubparanum=\dimen158
\foot@parindent=\dimen159
\c@theorem=\count272
\c@case=\count273
\c@conjecture=\count274
\c@corollary=\count275
\c@definition=\count276
\c@example=\count277
\c@exercise=\count278
\c@lemma=\count279
\c@note=\count280
\c@problem=\count281
\c@property=\count282
\c@proposition=\count283
\c@question=\count284
\c@solution=\count285
\c@remark=\count286
\c@prob=\count287
\instindent=\dimen160
\figgap=\dimen161
\bildb@x=\box52
\figcapgap=\dimen162
\tabcapgap=\dimen163
\c@merk=\count288
\c@@inst=\count289
\c@@auth=\count290
\c@auco=\count291
\instindent=\dimen164
\authrun=\box53
\authorrunning=\toks17
\tocauthor=\toks18
\titrun=\box54
\titlerunning=\toks19
\toctitle=\toks20
\c@contribution=\count292
LaTeX Info: Redefining \abstract on input line 2385.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2024/09/29 v3.02 LaTeX color extensions (UK)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
) (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1349.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1353.
Package xcolor Info: Model `RGB' extended on input line 1365.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1367.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1371.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1372.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/xcolor/x11nam.def
File: x11nam.def 2024/09/29 v3.02 Predefined colors according to Unix/X11 (UK)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/amscls/amsthm.sty
Package: amsthm 2020/05/29 v2.20.6
\thm@style=\toks21
\thm@bodyfont=\toks22
\thm@headfont=\toks23
\thm@notefont=\toks24
\thm@headpunct=\toks25
\thm@preskip=\skip51
\thm@postskip=\skip52
\thm@headsep=\skip53
\dth@everypar=\toks26
LaTeX Info: Redefining \qed on input line 273.
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/framed/framed.sty
Package: framed 2011/10/22 v 0.96: framed or shaded text with page breaks
\OuterFrameSep=\skip54
\fb@frw=\dimen165
\fb@frh=\dimen166
\FrameRule=\dimen167
\FrameSep=\dimen168
) (/usr/local/texlive/2025/texmf-dist/tex/latex/psnfss/mathptmx.sty
Package: mathptmx 2020/03/25 PSNFSS-v9.3 Times w/ Math, improved (SPQR, WaS) 
LaTeX Font Info:    Redeclaring symbol font `operators' on input line 28.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> OT1/ztmcm/m/n on input line 28.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/ztmcm/m/n on input line 28.
LaTeX Font Info:    Redeclaring symbol font `letters' on input line 29.
LaTeX Font Info:    Overwriting symbol font `letters' in version `normal'
(Font)                  OML/cmm/m/it --> OML/ztmcm/m/it on input line 29.
LaTeX Font Info:    Overwriting symbol font `letters' in version `bold'
(Font)                  OML/cmm/b/it --> OML/ztmcm/m/it on input line 29.
LaTeX Font Info:    Redeclaring symbol font `symbols' on input line 30.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `normal'
(Font)                  OMS/cmsy/m/n --> OMS/ztmcm/m/n on input line 30.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `bold'
(Font)                  OMS/cmsy/b/n --> OMS/ztmcm/m/n on input line 30.
LaTeX Font Info:    Redeclaring symbol font `largesymbols' on input line 31.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `normal'
(Font)                  OMX/cmex/m/n --> OMX/ztmcm/m/n on input line 31.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `bold'
(Font)                  OMX/cmex/m/n --> OMX/ztmcm/m/n on input line 31.
\symbold=\mathgroup4
\symitalic=\mathgroup5
LaTeX Font Info:    Redeclaring math alphabet \mathbf on input line 34.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> OT1/ptm/bx/n on input line 34.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/ptm/bx/n on input line 34.
LaTeX Font Info:    Redeclaring math alphabet \mathit on input line 35.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> OT1/ptm/m/it on input line 35.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> OT1/ptm/m/it on input line 35.
LaTeX Info: Redefining \hbar on input line 50.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/psnfss/helvet.sty
Package: helvet 2020/03/25 PSNFSS-v9.3 (WaS) 
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks27
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/psnfss/courier.sty
Package: courier 2020/03/25 PSNFSS-v9.3 (WaS) 
) (/usr/local/texlive/2025/texmf-dist/tex/latex/type1cm/type1cm.sty
Package: type1cm 2002/09/05 v0.04 BlueSky/Y&Y Type1 CM font definitions (DPC, patched RF)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/base/makeidx.sty
Package: makeidx 2021/10/04 v1.0m Standard LaTeX package
) (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.
)
\Gin@req@height=\dimen169
\Gin@req@width=\dimen170
) (/usr/local/texlive/2025/texmf-dist/tex/latex/tools/multicol.sty
Package: multicol 2024/09/14 v1.9i multicolumn formatting (FMi)
\c@tracingmulticols=\count293
\mult@box=\box55
\multicol@leftmargin=\dimen171
\c@unbalance=\count294
\c@collectmore=\count295
\doublecol@number=\count296
\multicoltolerance=\count297
\multicolpretolerance=\count298
\full@width=\dimen172
\page@free=\dimen173
\premulticols=\dimen174
\postmulticols=\dimen175
\multicolsep=\skip55
\multicolbaselineskip=\skip56
\partial@page=\box56
\last@line=\box57
\mc@boxedresult=\box58
\maxbalancingoverflow=\dimen176
\mult@rightbox=\box59
\mult@grightbox=\box60
\mult@firstbox=\box61
\mult@gfirstbox=\box62
\@tempa=\box63
\@tempa=\box64
\@tempa=\box65
\@tempa=\box66
\@tempa=\box67
\@tempa=\box68
\@tempa=\box69
\@tempa=\box70
\@tempa=\box71
\@tempa=\box72
\@tempa=\box73
\@tempa=\box74
\@tempa=\box75
\@tempa=\box76
\@tempa=\box77
\@tempa=\box78
\@tempa=\box79
\@tempa=\box80
\@tempa=\box81
\@tempa=\box82
\@tempa=\box83
\@tempa=\box84
\@tempa=\box85
\@tempa=\box86
\@tempa=\box87
\@tempa=\box88
\@tempa=\box89
\@tempa=\box90
\@tempa=\box91
\@tempa=\box92
\@tempa=\box93
\@tempa=\box94
\@tempa=\box95
\@tempa=\box96
\@tempa=\box97
\@tempa=\box98
\c@minrows=\count299
\c@columnbadness=\count300
\c@finalcolumnbadness=\count301
\last@try=\dimen177
\multicolovershoot=\dimen178
\multicolundershoot=\dimen179
\mult@nat@firstbox=\box99
\colbreak@box=\box100
\mc@col@check@num=\count302
) (/usr/local/texlive/2025/texmf-dist/tex/latex/footmisc/footmisc.sty
Package: footmisc 2024/12/24 v6.0g a miscellany of footnote facilities
\FN@temptoken=\toks28
\footnotemargin=\dimen180
\@outputbox@depth=\dimen181
Package footmisc Info: Declaring symbol style bringhurst on input line 699.
Package footmisc Info: Declaring symbol style chicago on input line 707.
Package footmisc Info: Declaring symbol style wiley on input line 716.
Package footmisc Info: Declaring symbol style lamport-robust on input line 727.
Package footmisc Info: Declaring symbol style lamport* on input line 747.
Package footmisc Info: Declaring symbol style lamport*-robust on input line 768.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2024/11/05 v2.17t AMS math features
\@mathmargin=\skip57

For additional information on amsmath, use the `?' option.
(/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks29
\ex@=\dimen182
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen183
) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count303
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count304
\leftroot@=\count305
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count306
\DOTSCASE@=\count307
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box101
\strutbox@=\box102
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen184
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count308
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count309
\dotsspace@=\muskip17
\c@parentequation=\count310
\dspbrk@lvl=\count311
\tag@help=\toks30
\row@=\count312
\column@=\count313
\maxfields@=\count314
\andhelp@=\toks31
\eqnshift@=\dimen185
\alignsep@=\dimen186
\tagshift@=\dimen187
\tagwidth@=\dimen188
\totwidth@=\dimen189
\lineht@=\dimen190
\@envbody=\toks32
\multlinegap=\skip58
\multlinetaggap=\skip59
\mathdisplay@stack=\toks33
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup6
\symAMSb=\mathgroup7
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
) (/usr/local/texlive/2025/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip18
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/caption/subcaption.sty
Package: subcaption 2023/07/28 v1.6b Sub-captions (AR)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/caption/caption.sty
Package: caption 2023/08/05 v3.6o Customizing captions (AR)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/caption/caption3.sty
Package: caption3 2023/07/31 v2.4d caption3 kernel (AR)
\caption@tempdima=\dimen191
\captionmargin=\dimen192
\caption@leftmargin=\dimen193
\caption@rightmargin=\dimen194
\caption@width=\dimen195
\caption@indent=\dimen196
\caption@parindent=\dimen197
\caption@hangindent=\dimen198
Package caption Info: Unknown document class (or package),
(caption)             standard defaults will be used.
Package caption Info: \@makecaption = \long macro:#1#2->\captionstyle \ifx \@captype \fig@type \vskip \figcapgap \fi \setbox \@tempboxa \hbox {{\floatlegendstyle #1\floatcounterend }\capstrut #2}\ifdim \wd \@tempboxa >\hsize {\floatlegendstyle #1\floatcounterend }\capstrut #2\par \else \hbox to\hsize {\leftlegendglue \unhbox \@tempboxa \hfil }\fi \ifx \@captype \fig@type \else \vskip \tabcapgap \fi  on input line 1175.
)

Package caption Warning: Unknown document class (or package),
(caption)                standard defaults will be used.
See the caption package documentation for explanation.

\c@caption@flags=\count315
\c@continuedfloat=\count316
)
Package caption Info: New subtype `subfigure' on input line 238.
\c@subfigure=\count317
Package caption Info: New subtype `subtable' on input line 238.
\c@subtable=\count318
) (/usr/local/texlive/2025/texmf-dist/tex/latex/multirow/multirow.sty
Package: multirow 2024/11/12 v2.9 Span multiple rows of a table
\multirow@colwidth=\skip60
\multirow@cntb=\count319
\multirow@dima=\skip61
\bigstrutjot=\dimen199
)
\@indexfile=\write4
\openout4 = `author302.idx'.


Writing index file author302.idx
LaTeX Font Info:    Trying to load font information for OT1+ptm on input line 32.
(/usr/local/texlive/2025/texmf-dist/tex/latex/psnfss/ot1ptm.fd
File: ot1ptm.fd 2001/06/04 font definitions for OT1/ptm.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2024-05-08 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count320
\l__pdf_internal_box=\box103
) (./author302.aux)
\openout1 = `author302.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 32.
LaTeX Font Info:    ... okay on input line 32.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 32.
LaTeX Font Info:    ... okay on input line 32.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 32.
LaTeX Font Info:    ... okay on input line 32.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 32.
LaTeX Font Info:    ... okay on input line 32.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 32.
LaTeX Font Info:    ... okay on input line 32.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 32.
LaTeX Font Info:    ... okay on input line 32.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 32.
LaTeX Font Info:    ... okay on input line 32.
 (/usr/local/texlive/2025/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count321
\scratchdimen=\dimen256
\scratchbox=\box104
\nofMPsegments=\count322
\nofMParguments=\count323
\everyMPshowfont=\toks34
\MPscratchCnt=\count324
\MPscratchDim=\dimen257
\MPnumerator=\count325
\makeMPintoPDFobject=\count326
\everyMPtoPDFconversion=\toks35
) (/usr/local/texlive/2025/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 485.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Live
))
Package caption Info: Begin \AtBeginDocument code.
Package caption Info: End \AtBeginDocument code.


Package mathptmx Warning: There are no bold math fonts on input line 47.

LaTeX Font Info:    Calculating math sizes for size <8.5> on input line 47.
LaTeX Font Info:    Trying to load font information for OT1+ztmcm on input line 47.
(/usr/local/texlive/2025/texmf-dist/tex/latex/psnfss/ot1ztmcm.fd
File: ot1ztmcm.fd 2000/01/03 Fontinst v1.801 font definitions for OT1/ztmcm.
)
LaTeX Font Info:    Trying to load font information for OML+ztmcm on input line 47.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/psnfss/omlztmcm.fd
File: omlztmcm.fd 2000/01/03 Fontinst v1.801 font definitions for OML/ztmcm.
)
LaTeX Font Info:    Trying to load font information for OMS+ztmcm on input line 47.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/psnfss/omsztmcm.fd
File: omsztmcm.fd 2000/01/03 Fontinst v1.801 font definitions for OMS/ztmcm.
)
LaTeX Font Info:    Trying to load font information for OMX+ztmcm on input line 47.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/psnfss/omxztmcm.fd
File: omxztmcm.fd 2000/01/03 Fontinst v1.801 font definitions for OMX/ztmcm.
)
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <8.5> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 47.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <6.29004> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 47.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <5.10005> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 47.
LaTeX Font Info:    Trying to load font information for OT1+pcr on input line 47.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/psnfss/ot1pcr.fd
File: ot1pcr.fd 2001/06/04 font definitions for OT1/pcr.
)
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <10> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 47.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <7.4> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 47.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <6> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 47.


[1



{/usr/local/texlive/2025/texmf-var/fonts/map/pdftex/updmap/pdftex.map}{/usr/local/texlive/2025/texmf-dist/fonts/enc/dvips/base/8r.enc}]
<fig302/Fig. 1. The time relationship chart between ECG and PPG.png, id=9, 699.048pt x 530.418pt>
File: fig302/Fig. 1. The time relationship chart between ECG and PPG.png Graphic file (type png)
<use fig302/Fig. 1. The time relationship chart between ECG and PPG.png>
Package pdftex.def Info: fig302/Fig. 1. The time relationship chart between ECG and PPG.png  used on input line 62.
(pdftex.def)             Requested size: 299.60547pt x 227.33069pt.


[2 <./fig302/Fig. 1. The time relationship chart between ECG and PPG.png>]
<fig302/Fig. 2. The multi-modal model architecture for predicting AF.png, id=15, 5437.31375pt x 2476.25125pt>
File: fig302/Fig. 2. The multi-modal model architecture for predicting AF.png Graphic file (type png)
<use fig302/Fig. 2. The multi-modal model architecture for predicting AF.png>
Package pdftex.def Info: fig302/Fig. 2. The multi-modal model architecture for predicting AF.png  used on input line 79.
(pdftex.def)             Requested size: 299.60547pt x 136.43983pt.


[3 <./fig302/Fig. 2. The multi-modal model architecture for predicting AF.png>]
Underfull \hbox (badness 4060) in paragraph at lines 98--98
[]\OT1/ptm/m/n/8.5 2 classes AF:19
 []


Underfull \hbox (badness 10000) in paragraph at lines 98--98
[]\OT1/ptm/m/n/8.5 Bedside mon-i-tor
 []



[4]
<fig302/Fig. 3. (a) ECG.png, id=23, 3308.36pt x 2484.28125pt>
File: fig302/Fig. 3. (a) ECG.png Graphic file (type png)
<use fig302/Fig. 3. (a) ECG.png>
Package pdftex.def Info: fig302/Fig. 3. (a) ECG.png  used on input line 142.
(pdftex.def)             Requested size: 163.12146pt x 122.47762pt.
<fig302/Fig. 3. (b) PPG.png, id=24, 1927.2pt x 1445.4pt>
File: fig302/Fig. 3. (b) PPG.png Graphic file (type png)
<use fig302/Fig. 3. (b) PPG.png>
Package pdftex.def Info: fig302/Fig. 3. (b) PPG.png  used on input line 148.
(pdftex.def)             Requested size: 163.12146pt x 122.33908pt.


[5]
<fig302/Fig. 4 Sig-CLNet architecture diagram.png, id=28, 4456.65pt x 1449.415pt>
File: fig302/Fig. 4 Sig-CLNet architecture diagram.png Graphic file (type png)
<use fig302/Fig. 4 Sig-CLNet architecture diagram.png>
Package pdftex.def Info: fig302/Fig. 4 Sig-CLNet architecture diagram.png  used on input line 175.
(pdftex.def)             Requested size: 332.89723pt x 108.25911pt.


[6 <./fig302/Fig. 3. (a) ECG.png> <./fig302/Fig. 3. (b) PPG.png> <./fig302/Fig. 4 Sig-CLNet architecture diagram.png>]
<fig302/Fig. 5. (a) A2CA4C Detection.png, id=33, 2484.28125pt x 2484.28125pt>
File: fig302/Fig. 5. (a) A2CA4C Detection.png Graphic file (type png)
<use fig302/Fig. 5. (a) A2CA4C Detection.png>
Package pdftex.def Info: fig302/Fig. 5. (a) A2CA4C Detection.png  used on input line 202.
(pdftex.def)             Requested size: 106.52954pt x 106.51875pt.
<fig302/Fig. 5. (b) LALV Segmentation.png, id=34, 2484.28125pt x 2484.28125pt>
File: fig302/Fig. 5. (b) LALV Segmentation.png Graphic file (type png)
<use fig302/Fig. 5. (b) LALV Segmentation.png>
Package pdftex.def Info: fig302/Fig. 5. (b) LALV Segmentation.png  used on input line 209.
(pdftex.def)             Requested size: 106.52954pt x 106.51875pt.
<fig302/Fig. 5. (c) RV Segmentation.png, id=35, 2484.28125pt x 2484.28125pt>
File: fig302/Fig. 5. (c) RV Segmentation.png Graphic file (type png)
<use fig302/Fig. 5. (c) RV Segmentation.png>
Package pdftex.def Info: fig302/Fig. 5. (c) RV Segmentation.png  used on input line 216.
(pdftex.def)             Requested size: 106.52954pt x 106.51875pt.
<fig302/Fig. 6. (a) LA Segmentation.jpg, id=36, 642.4pt x 642.4pt>
File: fig302/Fig. 6. (a) LA Segmentation.jpg Graphic file (type jpg)
<use fig302/Fig. 6. (a) LA Segmentation.jpg>
Package pdftex.def Info: fig302/Fig. 6. (a) LA Segmentation.jpg  used on input line 228.
(pdftex.def)             Requested size: 106.52954pt x 106.52074pt.
<fig302/Fig. 6. (b) LV Segmentation.jpg, id=37, 642.4pt x 642.4pt>
File: fig302/Fig. 6. (b) LV Segmentation.jpg Graphic file (type jpg)
<use fig302/Fig. 6. (b) LV Segmentation.jpg>
Package pdftex.def Info: fig302/Fig. 6. (b) LV Segmentation.jpg  used on input line 235.
(pdftex.def)             Requested size: 106.52954pt x 106.52074pt.
<fig302/Fig. 6. (c) RV Segmentation.jpg, id=38, 642.4pt x 642.4pt>
File: fig302/Fig. 6. (c) RV Segmentation.jpg Graphic file (type jpg)
<use fig302/Fig. 6. (c) RV Segmentation.jpg>
Package pdftex.def Info: fig302/Fig. 6. (c) RV Segmentation.jpg  used on input line 242.
(pdftex.def)             Requested size: 106.52954pt x 106.52074pt.
<fig302/Fig. 7. (a) Area.png, id=39, 617.30624pt x 462.72874pt>
File: fig302/Fig. 7. (a) Area.png Graphic file (type png)
<use fig302/Fig. 7. (a) Area.png>
Package pdftex.def Info: fig302/Fig. 7. (a) Area.png  used on input line 253.
(pdftex.def)             Requested size: 163.12146pt x 122.2695pt.
<fig302/Fig. 7. (b) Length.png, id=40, 617.30624pt x 462.72874pt>
File: fig302/Fig. 7. (b) Length.png Graphic file (type png)
<use fig302/Fig. 7. (b) Length.png>
Package pdftex.def Info: fig302/Fig. 7. (b) Length.png  used on input line 258.
(pdftex.def)             Requested size: 163.12146pt x 122.2695pt.
<fig302/Fig. 7. (c) Volume.png, id=41, 617.30624pt x 462.72874pt>
File: fig302/Fig. 7. (c) Volume.png Graphic file (type png)
<use fig302/Fig. 7. (c) Volume.png>
Package pdftex.def Info: fig302/Fig. 7. (c) Volume.png  used on input line 264.
(pdftex.def)             Requested size: 163.12146pt x 122.2695pt.
<fig302/Fig. 7. (d) Width.png, id=42, 617.30624pt x 462.72874pt>
File: fig302/Fig. 7. (d) Width.png Graphic file (type png)
<use fig302/Fig. 7. (d) Width.png>
Package pdftex.def Info: fig302/Fig. 7. (d) Width.png  used on input line 269.
(pdftex.def)             Requested size: 163.12146pt x 122.2695pt.


[7]
<fig302/Fig. 8. Ultra-CNet architecture diagram.png, id=51, 4227.795pt x 1593.955pt>
File: fig302/Fig. 8. Ultra-CNet architecture diagram.png Graphic file (type png)
<use fig302/Fig. 8. Ultra-CNet architecture diagram.png>
Package pdftex.def Info: fig302/Fig. 8. Ultra-CNet architecture diagram.png  used on input line 296.
(pdftex.def)             Requested size: 332.89723pt x 125.50029pt.


[8 <./fig302/Fig. 5. (a) A2CA4C Detection.png> <./fig302/Fig. 5. (b) LALV Segmentation.png> <./fig302/Fig. 5. (c) RV Segmentation.png> <./fig302/Fig. 6. (a) LA Segmentation.jpg> <./fig302/Fig. 6. (b) LV Segmentation.jpg> <./fig302/Fig. 6. (c) RV Segmentation.jpg>]

[9 <./fig302/Fig. 7. (a) Area.png> <./fig302/Fig. 7. (b) Length.png> <./fig302/Fig. 7. (c) Volume.png> <./fig302/Fig. 7. (d) Width.png> <./fig302/Fig. 8. Ultra-CNet architecture diagram.png>]
<fig302/Fig. 9. The final MLP architecture diagram.png, id=66, 2750.275pt x 2128.95375pt>
File: fig302/Fig. 9. The final MLP architecture diagram.png Graphic file (type png)
<use fig302/Fig. 9. The final MLP architecture diagram.png>
Package pdftex.def Info: fig302/Fig. 9. The final MLP architecture diagram.png  used on input line 323.
(pdftex.def)             Requested size: 332.89723pt x 257.67242pt.


[10 <./fig302/Fig. 9. The final MLP architecture diagram.png>]

[11]

[12]

[13]
Underfull \hbox (badness 1320) in paragraph at lines 447--447
[]\OT1/ptm/m/n/8.5 Automated Car-dio-vas-cu-lar Dis-
 []


Underfull \hbox (badness 2452) in paragraph at lines 448--448
[]\OT1/ptm/m/n/8.5 Risk As-sess-ment for Is-chemic
 []


Underfull \hbox (badness 10000) in paragraph at lines 448--448
[]\OT1/ptm/m/n/8.5 XGBoost, ma-chine
 []


Underfull \hbox (badness 5908) in paragraph at lines 449--449
[]\OT1/ptm/m/n/8.5 Detection of Acute Coro-nary
 []


Overfull \hbox (54.7622pt too wide) in paragraph at lines 443--455
 [][] 
 []

<fig302/Fig. 10. (a) Sig-CLNet accuracy.png, id=81, 617.30624pt x 462.72874pt>
File: fig302/Fig. 10. (a) Sig-CLNet accuracy.png Graphic file (type png)
<use fig302/Fig. 10. (a) Sig-CLNet accuracy.png>
Package pdftex.def Info: fig302/Fig. 10. (a) Sig-CLNet accuracy.png  used on input line 463.
(pdftex.def)             Requested size: 159.78925pt x 119.77708pt.
<fig302/Fig. 10. (b) Sig-CLNet loss.png, id=82, 617.30624pt x 462.72874pt>
File: fig302/Fig. 10. (b) Sig-CLNet loss.png Graphic file (type png)
<use fig302/Fig. 10. (b) Sig-CLNet loss.png>
Package pdftex.def Info: fig302/Fig. 10. (b) Sig-CLNet loss.png  used on input line 469.
(pdftex.def)             Requested size: 159.78925pt x 119.77708pt.
<fig302/Fig. 10. (c) Ultra-CNet accuracy.png, id=83, 617.30624pt x 462.72874pt>
File: fig302/Fig. 10. (c) Ultra-CNet accuracy.png Graphic file (type png)
<use fig302/Fig. 10. (c) Ultra-CNet accuracy.png>
Package pdftex.def Info: fig302/Fig. 10. (c) Ultra-CNet accuracy.png  used on input line 475.
(pdftex.def)             Requested size: 159.78925pt x 119.77708pt.
<fig302/Fig. 10. (d) Ultra-CNet loss.png, id=84, 617.30624pt x 462.72874pt>
File: fig302/Fig. 10. (d) Ultra-CNet loss.png Graphic file (type png)
<use fig302/Fig. 10. (d) Ultra-CNet loss.png>
Package pdftex.def Info: fig302/Fig. 10. (d) Ultra-CNet loss.png  used on input line 481.
(pdftex.def)             Requested size: 159.78925pt x 119.77708pt.
<fig302/Fig. 10. (e) The final MLP classifier accuracy.png, id=85, 617.30624pt x 462.72874pt>
File: fig302/Fig. 10. (e) The final MLP classifier accuracy.png Graphic file (type png)
<use fig302/Fig. 10. (e) The final MLP classifier accuracy.png>
Package pdftex.def Info: fig302/Fig. 10. (e) The final MLP classifier accuracy.png  used on input line 487.
(pdftex.def)             Requested size: 159.78925pt x 119.77708pt.
<fig302/Fig. 10. (f) The final MLP classifier loss.png, id=86, 617.30624pt x 462.72874pt>
File: fig302/Fig. 10. (f) The final MLP classifier loss.png Graphic file (type png)
<use fig302/Fig. 10. (f) The final MLP classifier loss.png>
Package pdftex.def Info: fig302/Fig. 10. (f) The final MLP classifier loss.png  used on input line 493.
(pdftex.def)             Requested size: 159.78925pt x 119.77708pt.


[14]
<fig302/Fig. 11. (a) Sig-CLNet confusion matrix.png, id=90, 617.30624pt x 462.72874pt>
File: fig302/Fig. 11. (a) Sig-CLNet confusion matrix.png Graphic file (type png)
<use fig302/Fig. 11. (a) Sig-CLNet confusion matrix.png>
Package pdftex.def Info: fig302/Fig. 11. (a) Sig-CLNet confusion matrix.png  used on input line 505.
(pdftex.def)             Requested size: 106.52954pt x 79.84903pt.
<fig302/Fig. 11. (b) Ultra-CNet confusion matrix.png, id=91, 617.30624pt x 462.72874pt>
File: fig302/Fig. 11. (b) Ultra-CNet confusion matrix.png Graphic file (type png)
<use fig302/Fig. 11. (b) Ultra-CNet confusion matrix.png>
Package pdftex.def Info: fig302/Fig. 11. (b) Ultra-CNet confusion matrix.png  used on input line 511.
(pdftex.def)             Requested size: 106.52954pt x 79.84903pt.
<fig302/Fig. 11. (c) The final MLP classifier confusion matrix.png, id=92, 617.30624pt x 462.72874pt>
File: fig302/Fig. 11. (c) The final MLP classifier confusion matrix.png Graphic file (type png)
<use fig302/Fig. 11. (c) The final MLP classifier confusion matrix.png>
Package pdftex.def Info: fig302/Fig. 11. (c) The final MLP classifier confusion matrix.png  used on input line 517.
(pdftex.def)             Requested size: 106.52954pt x 79.84903pt.


[15 <./fig302/Fig. 10. (a) Sig-CLNet accuracy.png> <./fig302/Fig. 10. (b) Sig-CLNet loss.png> <./fig302/Fig. 10. (c) Ultra-CNet accuracy.png> <./fig302/Fig. 10. (d) Ultra-CNet loss.png> <./fig302/Fig. 10. (e) The final MLP classifier accuracy.png> <./fig302/Fig. 10. (f) The final MLP classifier loss.png>] (./references302.tex

[16 <./fig302/Fig. 11. (a) Sig-CLNet confusion matrix.png> <./fig302/Fig. 11. (b) Ultra-CNet confusion matrix.png> <./fig302/Fig. 11. (c) The final MLP classifier confusion matrix.png>]
Underfull \hbox (badness 10000) in paragraph at lines 40--41
[]\OT1/ptm/m/n/8.5 "Vital signs," \OT1/ptm/m/it/8.5 Wikipedia, The Free En-cy-clo-pe-dia\OT1/ptm/m/n/8.5 . Avail-able on-line:
 []


Underfull \hbox (badness 1325) in paragraph at lines 40--41
\OT1/ptm/m/n/8.5 https://en.wikipedia.org/wiki/Vital[]signs (ac-cessed on the date of pa-per's con-text, e.g.,
 []



[17])

[18] (./author302.aux)
 ***********
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
 ***********
 ) 
Here is how much of TeX's memory you used:
 6647 strings out of 473190
 110672 string characters out of 5715800
 536265 words of memory out of 5000000
 29812 multiletter control sequences out of 15000+600000
 594605 words of font info for 108 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 67i,10n,85p,1235b,509s stack positions out of 10000i,1000n,20000p,200000b,200000s
</usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmr10.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy10.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/urw/courier/ucrr8a.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/urw/symbol/usyr.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/urw/times/utmb8a.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/urw/times/utmr8a.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/urw/times/utmri8a.pfb>
Output written on author302.pdf (18 pages, 12228772 bytes).
PDF statistics:
 146 PDF objects out of 1000 (max. 8388607)
 64 compressed objects within 1 object stream
 0 named destinations out of 1000 (max. 500000)
 131 words of extra memory for PDF output out of 10000 (max. 10000000)

