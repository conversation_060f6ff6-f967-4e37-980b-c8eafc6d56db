This is pdfTeX, Version 3.141592653-2.6-1.40.27 (TeX Live 2025) (preloaded format=pdflatex 2025.3.22)  31 MAY 2025 00:52
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**/Users/<USER>/Documents/GitHub/cai-fm4healthcare-workshop/editor/author302.tex
(/Users/<USER>/Documents/GitHub/cai-fm4healthcare-workshop/editor/author302.tex
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
(./svmult.cls
Document Class: svmult 2024/03/15 v5.11 
Springer Verlag global LaTeX document class for multi authored books
Class Springer-SVMult Info: extra/valid Springer sub-package 
(Springer-SVMult)           not found in option list - using "global" style.
(/usr/local/texlive/2025/texmf-dist/tex/latex/base/article.cls
Document Class: article 2024/06/29 v1.4n Standard LaTeX document class
(/usr/local/texlive/2025/texmf-dist/tex/latex/base/size10.clo
File: size10.clo 2024/06/29 v1.4n Standard LaTeX file (size option)
)
\c@part=\count196
\c@section=\count197
\c@subsection=\count198
\c@subsubsection=\count199
\c@paragraph=\count266
\c@subparagraph=\count267
\c@figure=\count268
\c@table=\count269
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
)
\svparindent=\dimen142
\bibindent=\dimen143
\betweenumberspace=\dimen144
\headlineindent=\dimen145
\minitoc=\write3
\c@minitocdepth=\count270
\c@chapter=\count271
\mottowidth=\dimen146
\svitemindent=\dimen147
\verbatimindent=\dimen148
LaTeX Font Info:    Redeclaring math symbol \Gamma on input line 975.
LaTeX Font Info:    Redeclaring math symbol \Delta on input line 976.
LaTeX Font Info:    Redeclaring math symbol \Theta on input line 977.
LaTeX Font Info:    Redeclaring math symbol \Lambda on input line 978.
LaTeX Font Info:    Redeclaring math symbol \Xi on input line 979.
LaTeX Font Info:    Redeclaring math symbol \Pi on input line 980.
LaTeX Font Info:    Redeclaring math symbol \Sigma on input line 981.
LaTeX Font Info:    Redeclaring math symbol \Upsilon on input line 982.
LaTeX Font Info:    Redeclaring math symbol \Phi on input line 983.
LaTeX Font Info:    Redeclaring math symbol \Psi on input line 984.
LaTeX Font Info:    Redeclaring math symbol \Omega on input line 985.
\tocchpnum=\dimen149
\tocsecnum=\dimen150
\tocsectotal=\dimen151
\tocsubsecnum=\dimen152
\tocsubsectotal=\dimen153
\tocsubsubsecnum=\dimen154
\tocsubsubsectotal=\dimen155
\tocparanum=\dimen156
\tocparatotal=\dimen157
\tocsubparanum=\dimen158
\foot@parindent=\dimen159
\c@theorem=\count272
\c@case=\count273
\c@conjecture=\count274
\c@corollary=\count275
\c@definition=\count276
\c@example=\count277
\c@exercise=\count278
\c@lemma=\count279
\c@note=\count280
\c@problem=\count281
\c@property=\count282
\c@proposition=\count283
\c@question=\count284
\c@solution=\count285
\c@remark=\count286
\c@prob=\count287
\instindent=\dimen160
\figgap=\dimen161
\bildb@x=\box52
\figcapgap=\dimen162
\tabcapgap=\dimen163
\c@merk=\count288
\c@@inst=\count289
\c@@auth=\count290
\c@auco=\count291
\instindent=\dimen164
\authrun=\box53
\authorrunning=\toks17
\tocauthor=\toks18
\titrun=\box54
\titlerunning=\toks19
\toctitle=\toks20
\c@contribution=\count292
LaTeX Info: Redefining \abstract on input line 2385.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2024/09/29 v3.02 LaTeX color extensions (UK)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
) (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1349.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1353.
Package xcolor Info: Model `RGB' extended on input line 1365.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1367.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1371.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1372.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/xcolor/x11nam.def
File: x11nam.def 2024/09/29 v3.02 Predefined colors according to Unix/X11 (UK)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/amscls/amsthm.sty
Package: amsthm 2020/05/29 v2.20.6
\thm@style=\toks21
\thm@bodyfont=\toks22
\thm@headfont=\toks23
\thm@notefont=\toks24
\thm@headpunct=\toks25
\thm@preskip=\skip51
\thm@postskip=\skip52
\thm@headsep=\skip53
\dth@everypar=\toks26
LaTeX Info: Redefining \qed on input line 273.
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/framed/framed.sty
Package: framed 2011/10/22 v 0.96: framed or shaded text with page breaks
\OuterFrameSep=\skip54
\fb@frw=\dimen165
\fb@frh=\dimen166
\FrameRule=\dimen167
\FrameSep=\dimen168
) (/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/newtxtext.sty
Package: newtxtext 2024/04/01 v1.744(Michael Sharpe) latex and unicode latex support for TeXGyreTermesX
 `newtxtext' v1.744, 2024/04/01 Text macros taking advantage of TeXGyre Termes and its extensions (msharpe) (/usr/local/texlive/2025/texmf-dist/tex/latex/xpatch/xpatch.sty (/usr/local/texlive/2025/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2025-01-18 L3 programming layer (loader) 
 (/usr/local/texlive/2025/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2024-05-08 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count293
\l__pdf_internal_box=\box55
))
Package: xpatch 2020/03/25 v0.3a Extending etoolbox patching commands
 (/usr/local/texlive/2025/texmf-dist/tex/latex/l3packages/xparse/xparse.sty
Package: xparse 2024-08-16 L3 Experimental document command parser
) (/usr/local/texlive/2025/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2025/02/11 v2.5l e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count294
)) (/usr/local/texlive/2025/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
) (/usr/local/texlive/2025/texmf-dist/tex/latex/xkeyval/xkeyval.sty
Package: xkeyval 2022/06/16 v2.9 package option processing (HA)
 (/usr/local/texlive/2025/texmf-dist/tex/generic/xkeyval/xkeyval.tex (/usr/local/texlive/2025/texmf-dist/tex/generic/xkeyval/xkvutils.tex
\XKV@toks=\toks27
\XKV@tempa@toks=\toks28
 (/usr/local/texlive/2025/texmf-dist/tex/generic/xkeyval/keyval.tex))
\XKV@depth=\count295
File: xkeyval.tex 2014/12/03 v2.7a key=value parser (HA)
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/base/textcomp.sty
Package: textcomp 2024/04/24 v2.1b Standard LaTeX package
) (/usr/local/texlive/2025/texmf-dist/tex/generic/xstring/xstring.sty (/usr/local/texlive/2025/texmf-dist/tex/generic/xstring/xstring.tex
\xs_counta=\count296
\xs_countb=\count297
)
Package: xstring 2023/08/22 v1.86 String manipulations (CT)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2024/03/16 v1.1e Standard LaTeX ifthen package (DPC)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/carlisle/scalefnt.sty)
LaTeX Font Info:    Setting ntxLF sub-encoding to TS1/0 on input line 24.
LaTeX Font Info:    Setting ntxTLF sub-encoding to TS1/0 on input line 24.
LaTeX Font Info:    Setting ntxOsF sub-encoding to TS1/0 on input line 24.
LaTeX Font Info:    Setting ntxTOsF sub-encoding to TS1/0 on input line 24.
 (/usr/local/texlive/2025/texmf-dist/tex/generic/kastrup/binhex.tex)
\ntx@tmpcnta=\count298
\ntx@cnt=\count299
 (/usr/local/texlive/2025/texmf-dist/tex/latex/fontaxes/fontaxes.sty
Package: fontaxes 2020/07/21 v1.0e Font selection axes
LaTeX Info: Redefining \upshape on input line 29.
LaTeX Info: Redefining \itshape on input line 31.
LaTeX Info: Redefining \slshape on input line 33.
LaTeX Info: Redefining \swshape on input line 35.
LaTeX Info: Redefining \scshape on input line 37.
LaTeX Info: Redefining \sscshape on input line 39.
LaTeX Info: Redefining \ulcshape on input line 41.
LaTeX Info: Redefining \textsw on input line 47.
LaTeX Info: Redefining \textssc on input line 48.
LaTeX Info: Redefining \textulc on input line 49.
)
\tx@sixem=\dimen169
\tx@y=\dimen170
\tx@x=\dimen171
\tx@tmpdima=\dimen172
\tx@tmpdimb=\dimen173
\tx@tmpdimc=\dimen174
\tx@tmpdimd=\dimen175
\tx@tmpdime=\dimen176
\tx@tmpdimf=\dimen177
\tx@dimA=\dimen178
\tx@dimAA=\dimen179
\tx@dimB=\dimen180
\tx@dimBB=\dimen181
\tx@dimC=\dimen182
LaTeX Info: Redefining \oldstylenums on input line 902.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/newtxmath.sty
Package: newtxmath 2024/09/22 v1.754
 `newtxmath' v1.754, 2024/09/22 Math macros based originally on txfonts (msharpe) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2024/11/05 v2.17t AMS math features
\@mathmargin=\skip55

For additional information on amsmath, use the `?' option.
(/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks29
\ex@=\dimen183
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen184
) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count300
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count301
\leftroot@=\count302
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count303
\DOTSCASE@=\count304
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box56
\strutbox@=\box57
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen185
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count305
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count306
\dotsspace@=\muskip17
\c@parentequation=\count307
\dspbrk@lvl=\count308
\tag@help=\toks30
\row@=\count309
\column@=\count310
\maxfields@=\count311
\andhelp@=\toks31
\eqnshift@=\dimen186
\alignsep@=\dimen187
\tagshift@=\dimen188
\tagwidth@=\dimen189
\totwidth@=\dimen190
\lineht@=\dimen191
\@envbody=\toks32
\multlinegap=\skip56
\multlinetaggap=\skip57
\mathdisplay@stack=\toks33
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/oberdiek/centernot.sty
Package: centernot 2016/05/16 v1.4 Centers the not symbol horizontally (HO)
)
\tx@cntz=\count312
 (/usr/local/texlive/2025/texmf-dist/tex/generic/kastrup/binhex.tex)
\tx@Isdigit=\count313
\tx@IsAlNum=\count314
\tx@tA=\toks34
\tx@tB=\toks35
\tx@su=\read2

amsthm NOT loaded
LaTeX Font Info:    Redeclaring symbol font `operators' on input line 402.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> OT1/minntx/m/n on input line 402.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/minntx/m/n on input line 402.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/minntx/m/n --> OT1/minntx/b/n on input line 403.
LaTeX Font Info:    Redeclaring math alphabet \mathsf on input line 410.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> OT1/qhv/m/n on input line 410.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> OT1/qhv/m/n on input line 410.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/qhv/m/n --> OT1/qhv/b/n on input line 412.
LaTeX Font Info:    Redeclaring math alphabet \mathit on input line 419.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> OT1/minntx/m/it on input line 419.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> OT1/minntx/m/it on input line 419.
LaTeX Font Info:    Redeclaring math alphabet \mathtt on input line 420.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> OT1/ntxtt/m/n on input line 420.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> OT1/ntxtt/m/n on input line 420.
LaTeX Font Info:    Redeclaring math alphabet \mathbf on input line 422.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> OT1/minntx/b/n on input line 422.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/minntx/b/n on input line 422.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/minntx/m/it --> OT1/minntx/b/it on input line 423.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/ntxtt/m/n --> OT1/ntxtt/b/n on input line 426.
LaTeX Font Info:    Redeclaring symbol font `letters' on input line 534.
LaTeX Font Info:    Overwriting symbol font `letters' in version `normal'
(Font)                  OML/cmm/m/it --> OML/ntxmi/m/it on input line 534.
LaTeX Font Info:    Overwriting symbol font `letters' in version `bold'
(Font)                  OML/cmm/b/it --> OML/ntxmi/m/it on input line 534.
LaTeX Font Info:    Overwriting symbol font `letters' in version `bold'
(Font)                  OML/ntxmi/m/it --> OML/ntxmi/b/it on input line 535.
\symlettersA=\mathgroup4
LaTeX Font Info:    Overwriting symbol font `lettersA' in version `bold'
(Font)                  U/ntxmia/m/it --> U/ntxmia/b/it on input line 582.
Now handling font encoding LMS ...
... no UTF-8 mapping file for font encoding LMS
LaTeX Font Info:    Redeclaring symbol font `symbols' on input line 604.
LaTeX Font Info:    Encoding `OMS' has changed to `LMS' for symbol font
(Font)              `symbols' in the math version `normal' on input line 604.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `normal'
(Font)                  OMS/cmsy/m/n --> LMS/ntxsy/m/n on input line 604.
LaTeX Font Info:    Encoding `OMS' has changed to `LMS' for symbol font
(Font)              `symbols' in the math version `bold' on input line 604.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `bold'
(Font)                  OMS/cmsy/b/n --> LMS/ntxsy/m/n on input line 604.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `bold'
(Font)                  LMS/ntxsy/m/n --> LMS/ntxsy/b/n on input line 605.
\symAMSm=\mathgroup5
LaTeX Font Info:    Overwriting symbol font `AMSm' in version `bold'
(Font)                  U/ntxsym/m/n --> U/ntxsym/b/n on input line 630.
\symsymbolsC=\mathgroup6
LaTeX Font Info:    Overwriting symbol font `symbolsC' in version `bold'
(Font)                  U/ntxsyc/m/n --> U/ntxsyc/b/n on input line 651.
Now handling font encoding LMX ...
... no UTF-8 mapping file for font encoding LMX
LaTeX Font Info:    Redeclaring symbol font `largesymbols' on input line 664.
LaTeX Font Info:    Encoding `OMX' has changed to `LMX' for symbol font
(Font)              `largesymbols' in the math version `normal' on input line 664.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `normal'
(Font)                  OMX/cmex/m/n --> LMX/ntxexx/m/n on input line 664.
LaTeX Font Info:    Encoding `OMX' has changed to `LMX' for symbol font
(Font)              `largesymbols' in the math version `bold' on input line 664.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `bold'
(Font)                  OMX/cmex/m/n --> LMX/ntxexx/m/n on input line 664.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `bold'
(Font)                  LMX/ntxexx/m/n --> LMX/ntxexx/b/n on input line 665.
\symlargesymbolsTXA=\mathgroup7
LaTeX Font Info:    Overwriting symbol font `largesymbolsTXA' in version `bold'
(Font)                  U/ntxexa/m/n --> U/ntxexa/b/n on input line 679.
\tx@sbptoks=\toks36
LaTeX Font Info:    Redeclaring math delimiter \lfloor on input line 902.
LaTeX Font Info:    Redeclaring math delimiter \rfloor on input line 903.
LaTeX Font Info:    Redeclaring math delimiter \lceil on input line 904.
LaTeX Font Info:    Redeclaring math delimiter \rceil on input line 905.
LaTeX Font Info:    Redeclaring math delimiter \lbrace on input line 910.
LaTeX Font Info:    Redeclaring math delimiter \rbrace on input line 911.
LaTeX Font Info:    Redeclaring math delimiter \langle on input line 913.
LaTeX Font Info:    Redeclaring math delimiter \rangle on input line 915.
LaTeX Font Info:    Redeclaring math delimiter \arrowvert on input line 919.
LaTeX Font Info:    Redeclaring math delimiter \vert on input line 920.
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 966.
LaTeX Font Info:    Redeclaring math accent \dot on input line 991.
LaTeX Font Info:    Redeclaring math accent \ddot on input line 992.
LaTeX Font Info:    Redeclaring math accent \vec on input line 2057.
LaTeX Info: Redefining \Bbbk on input line 2847.
LaTeX Info: Redefining \not on input line 2995.
LaTeX Info: Redefining \textsquare on input line 3025.
LaTeX Info: Redefining \openbox on input line 3027.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/psnfss/helvet.sty
Package: helvet 2020/03/25 PSNFSS-v9.3 (WaS) 
) (/usr/local/texlive/2025/texmf-dist/tex/latex/psnfss/courier.sty
Package: courier 2020/03/25 PSNFSS-v9.3 (WaS) 
) (/usr/local/texlive/2025/texmf-dist/tex/latex/type1cm/type1cm.sty
Package: type1cm 2002/09/05 v0.04 BlueSky/Y&Y Type1 CM font definitions (DPC, patched RF)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/base/makeidx.sty
Package: makeidx 2021/10/04 v1.0m Standard LaTeX package
) (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.
)
\Gin@req@height=\dimen192
\Gin@req@width=\dimen193
) (/usr/local/texlive/2025/texmf-dist/tex/latex/tools/multicol.sty
Package: multicol 2024/09/14 v1.9i multicolumn formatting (FMi)
\c@tracingmulticols=\count315
\mult@box=\box58
\multicol@leftmargin=\dimen194
\c@unbalance=\count316
\c@collectmore=\count317
\doublecol@number=\count318
\multicoltolerance=\count319
\multicolpretolerance=\count320
\full@width=\dimen195
\page@free=\dimen196
\premulticols=\dimen197
\postmulticols=\dimen198
\multicolsep=\skip58
\multicolbaselineskip=\skip59
\partial@page=\box59
\last@line=\box60
\mc@boxedresult=\box61
\maxbalancingoverflow=\dimen199
\mult@rightbox=\box62
\mult@grightbox=\box63
\mult@firstbox=\box64
\mult@gfirstbox=\box65
\@tempa=\box66
\@tempa=\box67
\@tempa=\box68
\@tempa=\box69
\@tempa=\box70
\@tempa=\box71
\@tempa=\box72
\@tempa=\box73
\@tempa=\box74
\@tempa=\box75
\@tempa=\box76
\@tempa=\box77
\@tempa=\box78
\@tempa=\box79
\@tempa=\box80
\@tempa=\box81
\@tempa=\box82
\@tempa=\box83
\@tempa=\box84
\@tempa=\box85
\@tempa=\box86
\@tempa=\box87
\@tempa=\box88
\@tempa=\box89
\@tempa=\box90
\@tempa=\box91
\@tempa=\box92
\@tempa=\box93
\@tempa=\box94
\@tempa=\box95
\@tempa=\box96
\@tempa=\box97
\@tempa=\box98
\@tempa=\box99
\@tempa=\box100
\@tempa=\box101
\c@minrows=\count321
\c@columnbadness=\count322
\c@finalcolumnbadness=\count323
\last@try=\dimen256
\multicolovershoot=\dimen257
\multicolundershoot=\dimen258
\mult@nat@firstbox=\box102
\colbreak@box=\box103
\mc@col@check@num=\count324
) (/usr/local/texlive/2025/texmf-dist/tex/latex/footmisc/footmisc.sty
Package: footmisc 2024/12/24 v6.0g a miscellany of footnote facilities
\FN@temptoken=\toks37
\footnotemargin=\dimen259
\@outputbox@depth=\dimen260
Package footmisc Info: Declaring symbol style bringhurst on input line 699.
Package footmisc Info: Declaring symbol style chicago on input line 707.
Package footmisc Info: Declaring symbol style wiley on input line 716.
Package footmisc Info: Declaring symbol style lamport-robust on input line 727.
Package footmisc Info: Declaring symbol style lamport* on input line 747.
Package footmisc Info: Declaring symbol style lamport*-robust on input line 768.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup8
\symAMSb=\mathgroup9
LaTeX Font Info:    Redeclaring math delimiter \ulcorner on input line 74.
LaTeX Font Info:    Redeclaring math delimiter \urcorner on input line 75.
LaTeX Font Info:    Redeclaring math delimiter \llcorner on input line 76.
LaTeX Font Info:    Redeclaring math delimiter \lrcorner on input line 77.
LaTeX Font Info:    Redeclaring math symbol \square on input line 141.
LaTeX Font Info:    Redeclaring math symbol \lozenge on input line 142.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
LaTeX Font Info:    Redeclaring math symbol \boxdot on input line 44.
LaTeX Font Info:    Redeclaring math symbol \boxplus on input line 45.
LaTeX Font Info:    Redeclaring math symbol \boxtimes on input line 46.
LaTeX Font Info:    Redeclaring math symbol \blacksquare on input line 48.
LaTeX Font Info:    Redeclaring math symbol \centerdot on input line 49.
LaTeX Font Info:    Redeclaring math symbol \blacklozenge on input line 51.
LaTeX Font Info:    Redeclaring math symbol \circlearrowright on input line 52.
LaTeX Font Info:    Redeclaring math symbol \circlearrowleft on input line 53.
LaTeX Font Info:    Redeclaring math symbol \leftrightharpoons on input line 56.
LaTeX Font Info:    Redeclaring math symbol \boxminus on input line 57.
LaTeX Font Info:    Redeclaring math symbol \Vdash on input line 58.
LaTeX Font Info:    Redeclaring math symbol \Vvdash on input line 59.
LaTeX Font Info:    Redeclaring math symbol \vDash on input line 60.
LaTeX Font Info:    Redeclaring math symbol \twoheadrightarrow on input line 61.
LaTeX Font Info:    Redeclaring math symbol \twoheadleftarrow on input line 62.
LaTeX Font Info:    Redeclaring math symbol \leftleftarrows on input line 63.
LaTeX Font Info:    Redeclaring math symbol \rightrightarrows on input line 64.
LaTeX Font Info:    Redeclaring math symbol \upuparrows on input line 65.
LaTeX Font Info:    Redeclaring math symbol \downdownarrows on input line 66.
LaTeX Font Info:    Redeclaring math symbol \upharpoonright on input line 67.
LaTeX Font Info:    Redeclaring math symbol \downharpoonright on input line 69.
LaTeX Font Info:    Redeclaring math symbol \upharpoonleft on input line 70.
LaTeX Font Info:    Redeclaring math symbol \downharpoonleft on input line 71.
LaTeX Font Info:    Redeclaring math symbol \rightarrowtail on input line 72.
LaTeX Font Info:    Redeclaring math symbol \leftarrowtail on input line 73.
LaTeX Font Info:    Redeclaring math symbol \leftrightarrows on input line 74.
LaTeX Font Info:    Redeclaring math symbol \rightleftarrows on input line 75.
LaTeX Font Info:    Redeclaring math symbol \Lsh on input line 76.
LaTeX Font Info:    Redeclaring math symbol \Rsh on input line 77.
LaTeX Font Info:    Redeclaring math symbol \leftrightsquigarrow on input line 79.
LaTeX Font Info:    Redeclaring math symbol \looparrowleft on input line 80.
LaTeX Font Info:    Redeclaring math symbol \looparrowright on input line 81.
LaTeX Font Info:    Redeclaring math symbol \circeq on input line 82.
LaTeX Font Info:    Redeclaring math symbol \succsim on input line 83.
LaTeX Font Info:    Redeclaring math symbol \gtrsim on input line 84.
LaTeX Font Info:    Redeclaring math symbol \gtrapprox on input line 85.
LaTeX Font Info:    Redeclaring math symbol \multimap on input line 86.
LaTeX Font Info:    Redeclaring math symbol \therefore on input line 87.
LaTeX Font Info:    Redeclaring math symbol \because on input line 88.
LaTeX Font Info:    Redeclaring math symbol \doteqdot on input line 89.
LaTeX Font Info:    Redeclaring math symbol \triangleq on input line 91.
LaTeX Font Info:    Redeclaring math symbol \precsim on input line 92.
LaTeX Font Info:    Redeclaring math symbol \lesssim on input line 93.
LaTeX Font Info:    Redeclaring math symbol \lessapprox on input line 94.
LaTeX Font Info:    Redeclaring math symbol \eqslantless on input line 95.
LaTeX Font Info:    Redeclaring math symbol \eqslantgtr on input line 96.
LaTeX Font Info:    Redeclaring math symbol \curlyeqprec on input line 97.
LaTeX Font Info:    Redeclaring math symbol \curlyeqsucc on input line 98.
LaTeX Font Info:    Redeclaring math symbol \preccurlyeq on input line 99.
LaTeX Font Info:    Redeclaring math symbol \leqq on input line 100.
LaTeX Font Info:    Redeclaring math symbol \leqslant on input line 101.
LaTeX Font Info:    Redeclaring math symbol \lessgtr on input line 102.
LaTeX Font Info:    Redeclaring math symbol \backprime on input line 103.
LaTeX Font Info:    Redeclaring math symbol \risingdotseq on input line 104.
LaTeX Font Info:    Redeclaring math symbol \fallingdotseq on input line 105.
LaTeX Font Info:    Redeclaring math symbol \succcurlyeq on input line 106.
LaTeX Font Info:    Redeclaring math symbol \geqq on input line 107.
LaTeX Font Info:    Redeclaring math symbol \geqslant on input line 108.
LaTeX Font Info:    Redeclaring math symbol \gtrless on input line 109.
LaTeX Font Info:    Redeclaring math symbol \bigstar on input line 117.
LaTeX Font Info:    Redeclaring math symbol \between on input line 118.
LaTeX Font Info:    Redeclaring math symbol \blacktriangledown on input line 119.
LaTeX Font Info:    Redeclaring math symbol \blacktriangleright on input line 120.
LaTeX Font Info:    Redeclaring math symbol \blacktriangleleft on input line 121.
LaTeX Font Info:    Redeclaring math symbol \vartriangle on input line 122.
LaTeX Font Info:    Redeclaring math symbol \blacktriangle on input line 123.
LaTeX Font Info:    Redeclaring math symbol \triangledown on input line 124.
LaTeX Font Info:    Redeclaring math symbol \eqcirc on input line 125.
LaTeX Font Info:    Redeclaring math symbol \lesseqgtr on input line 126.
LaTeX Font Info:    Redeclaring math symbol \gtreqless on input line 127.
LaTeX Font Info:    Redeclaring math symbol \lesseqqgtr on input line 128.
LaTeX Font Info:    Redeclaring math symbol \gtreqqless on input line 129.
LaTeX Font Info:    Redeclaring math symbol \Rrightarrow on input line 130.
LaTeX Font Info:    Redeclaring math symbol \Lleftarrow on input line 131.
LaTeX Font Info:    Redeclaring math symbol \veebar on input line 132.
LaTeX Font Info:    Redeclaring math symbol \barwedge on input line 133.
LaTeX Font Info:    Redeclaring math symbol \doublebarwedge on input line 134.
LaTeX Font Info:    Redeclaring math symbol \measuredangle on input line 137.
LaTeX Font Info:    Redeclaring math symbol \sphericalangle on input line 138.
LaTeX Font Info:    Redeclaring math symbol \varpropto on input line 139.
LaTeX Font Info:    Redeclaring math symbol \smallsmile on input line 140.
LaTeX Font Info:    Redeclaring math symbol \smallfrown on input line 141.
LaTeX Font Info:    Redeclaring math symbol \Subset on input line 142.
LaTeX Font Info:    Redeclaring math symbol \Supset on input line 143.
LaTeX Font Info:    Redeclaring math symbol \Cup on input line 144.
LaTeX Font Info:    Redeclaring math symbol \Cap on input line 146.
LaTeX Font Info:    Redeclaring math symbol \curlywedge on input line 148.
LaTeX Font Info:    Redeclaring math symbol \curlyvee on input line 149.
LaTeX Font Info:    Redeclaring math symbol \leftthreetimes on input line 150.
LaTeX Font Info:    Redeclaring math symbol \rightthreetimes on input line 151.
LaTeX Font Info:    Redeclaring math symbol \subseteqq on input line 152.
LaTeX Font Info:    Redeclaring math symbol \supseteqq on input line 153.
LaTeX Font Info:    Redeclaring math symbol \bumpeq on input line 154.
LaTeX Font Info:    Redeclaring math symbol \Bumpeq on input line 155.
LaTeX Font Info:    Redeclaring math symbol \lll on input line 156.
LaTeX Font Info:    Redeclaring math symbol \ggg on input line 158.
LaTeX Font Info:    Redeclaring math symbol \circledS on input line 160.
LaTeX Font Info:    Redeclaring math symbol \pitchfork on input line 161.
LaTeX Font Info:    Redeclaring math symbol \dotplus on input line 162.
LaTeX Font Info:    Redeclaring math symbol \backsim on input line 163.
LaTeX Font Info:    Redeclaring math symbol \backsimeq on input line 164.
LaTeX Font Info:    Redeclaring math symbol \complement on input line 165.
LaTeX Font Info:    Redeclaring math symbol \intercal on input line 166.
LaTeX Font Info:    Redeclaring math symbol \circledcirc on input line 167.
LaTeX Font Info:    Redeclaring math symbol \circledast on input line 168.
LaTeX Font Info:    Redeclaring math symbol \circleddash on input line 169.
LaTeX Font Info:    Redeclaring math symbol \lvertneqq on input line 171.
LaTeX Font Info:    Redeclaring math symbol \gvertneqq on input line 172.
LaTeX Font Info:    Redeclaring math symbol \nleq on input line 173.
LaTeX Font Info:    Redeclaring math symbol \ngeq on input line 174.
LaTeX Font Info:    Redeclaring math symbol \nless on input line 175.
LaTeX Font Info:    Redeclaring math symbol \ngtr on input line 176.
LaTeX Font Info:    Redeclaring math symbol \nprec on input line 177.
LaTeX Font Info:    Redeclaring math symbol \nsucc on input line 178.
LaTeX Font Info:    Redeclaring math symbol \lneqq on input line 179.
LaTeX Font Info:    Redeclaring math symbol \gneqq on input line 180.
LaTeX Font Info:    Redeclaring math symbol \nleqslant on input line 181.
LaTeX Font Info:    Redeclaring math symbol \ngeqslant on input line 182.
LaTeX Font Info:    Redeclaring math symbol \lneq on input line 183.
LaTeX Font Info:    Redeclaring math symbol \gneq on input line 184.
LaTeX Font Info:    Redeclaring math symbol \npreceq on input line 185.
LaTeX Font Info:    Redeclaring math symbol \nsucceq on input line 186.
LaTeX Font Info:    Redeclaring math symbol \precnsim on input line 187.
LaTeX Font Info:    Redeclaring math symbol \succnsim on input line 188.
LaTeX Font Info:    Redeclaring math symbol \lnsim on input line 189.
LaTeX Font Info:    Redeclaring math symbol \gnsim on input line 190.
LaTeX Font Info:    Redeclaring math symbol \nleqq on input line 191.
LaTeX Font Info:    Redeclaring math symbol \ngeqq on input line 192.
LaTeX Font Info:    Redeclaring math symbol \precneqq on input line 193.
LaTeX Font Info:    Redeclaring math symbol \succneqq on input line 194.
LaTeX Font Info:    Redeclaring math symbol \precnapprox on input line 195.
LaTeX Font Info:    Redeclaring math symbol \succnapprox on input line 196.
LaTeX Font Info:    Redeclaring math symbol \lnapprox on input line 197.
LaTeX Font Info:    Redeclaring math symbol \gnapprox on input line 198.
LaTeX Font Info:    Redeclaring math symbol \nsim on input line 199.
LaTeX Font Info:    Redeclaring math symbol \ncong on input line 200.
LaTeX Font Info:    Redeclaring math symbol \diagup on input line 201.
LaTeX Font Info:    Redeclaring math symbol \diagdown on input line 202.
LaTeX Font Info:    Redeclaring math symbol \varsubsetneq on input line 203.
LaTeX Font Info:    Redeclaring math symbol \varsupsetneq on input line 204.
LaTeX Font Info:    Redeclaring math symbol \nsubseteqq on input line 205.
LaTeX Font Info:    Redeclaring math symbol \nsupseteqq on input line 206.
LaTeX Font Info:    Redeclaring math symbol \subsetneqq on input line 207.
LaTeX Font Info:    Redeclaring math symbol \supsetneqq on input line 208.
LaTeX Font Info:    Redeclaring math symbol \varsubsetneqq on input line 209.
LaTeX Font Info:    Redeclaring math symbol \varsupsetneqq on input line 210.
LaTeX Font Info:    Redeclaring math symbol \subsetneq on input line 211.
LaTeX Font Info:    Redeclaring math symbol \supsetneq on input line 212.
LaTeX Font Info:    Redeclaring math symbol \nsubseteq on input line 213.
LaTeX Font Info:    Redeclaring math symbol \nsupseteq on input line 214.
LaTeX Font Info:    Redeclaring math symbol \nparallel on input line 215.
LaTeX Font Info:    Redeclaring math symbol \nmid on input line 216.
LaTeX Font Info:    Redeclaring math symbol \nshortmid on input line 217.
LaTeX Font Info:    Redeclaring math symbol \nshortparallel on input line 218.
LaTeX Font Info:    Redeclaring math symbol \nvdash on input line 219.
LaTeX Font Info:    Redeclaring math symbol \nVdash on input line 220.
LaTeX Font Info:    Redeclaring math symbol \nvDash on input line 221.
LaTeX Font Info:    Redeclaring math symbol \nVDash on input line 222.
LaTeX Font Info:    Redeclaring math symbol \ntrianglerighteq on input line 223.
LaTeX Font Info:    Redeclaring math symbol \ntrianglelefteq on input line 224.
LaTeX Font Info:    Redeclaring math symbol \ntriangleleft on input line 225.
LaTeX Font Info:    Redeclaring math symbol \ntriangleright on input line 226.
LaTeX Font Info:    Redeclaring math symbol \nleftarrow on input line 227.
LaTeX Font Info:    Redeclaring math symbol \nrightarrow on input line 228.
LaTeX Font Info:    Redeclaring math symbol \nLeftarrow on input line 229.
LaTeX Font Info:    Redeclaring math symbol \nRightarrow on input line 230.
LaTeX Font Info:    Redeclaring math symbol \nLeftrightarrow on input line 231.
LaTeX Font Info:    Redeclaring math symbol \nleftrightarrow on input line 232.
LaTeX Font Info:    Redeclaring math symbol \divideontimes on input line 233.
LaTeX Font Info:    Redeclaring math symbol \varnothing on input line 234.
LaTeX Font Info:    Redeclaring math symbol \nexists on input line 235.
LaTeX Font Info:    Redeclaring math symbol \Finv on input line 236.
LaTeX Font Info:    Redeclaring math symbol \Game on input line 237.
LaTeX Font Info:    Redeclaring math symbol \eth on input line 240.
LaTeX Font Info:    Redeclaring math symbol \eqsim on input line 241.
LaTeX Font Info:    Redeclaring math symbol \beth on input line 242.
LaTeX Font Info:    Redeclaring math symbol \gimel on input line 243.
LaTeX Font Info:    Redeclaring math symbol \daleth on input line 244.
LaTeX Font Info:    Redeclaring math symbol \lessdot on input line 245.
LaTeX Font Info:    Redeclaring math symbol \gtrdot on input line 246.
LaTeX Font Info:    Redeclaring math symbol \ltimes on input line 247.
LaTeX Font Info:    Redeclaring math symbol \rtimes on input line 248.
LaTeX Font Info:    Redeclaring math symbol \shortmid on input line 249.
LaTeX Font Info:    Redeclaring math symbol \shortparallel on input line 250.
LaTeX Font Info:    Redeclaring math symbol \smallsetminus on input line 251.
LaTeX Font Info:    Redeclaring math symbol \thicksim on input line 252.
LaTeX Font Info:    Redeclaring math symbol \thickapprox on input line 253.
LaTeX Font Info:    Redeclaring math symbol \approxeq on input line 254.
LaTeX Font Info:    Redeclaring math symbol \succapprox on input line 255.
LaTeX Font Info:    Redeclaring math symbol \precapprox on input line 256.
LaTeX Font Info:    Redeclaring math symbol \curvearrowleft on input line 257.
LaTeX Font Info:    Redeclaring math symbol \curvearrowright on input line 258.
LaTeX Font Info:    Redeclaring math symbol \digamma on input line 259.
LaTeX Font Info:    Redeclaring math symbol \varkappa on input line 260.


/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/amssymb.sty:261: LaTeX Error: Command `\Bbbk' already defined.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.261 ...ol{\Bbbk}           {\mathord}{AMSb}{"7C}
                                                  
Your command was ignored.
Type  I <command> <return>  to replace it with another command,
or  <return>  to continue without it.

LaTeX Font Info:    Redeclaring math symbol \hslash on input line 262.
LaTeX Font Info:    Redeclaring math symbol \backepsilon on input line 265.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/tools/bm.sty
Package: bm 2023/12/19 v1.2f Bold Symbol Support (DPC/FMi)
\symboldoperators=\mathgroup10
\symboldletters=\mathgroup11
\symboldsymbols=\mathgroup12
\symboldlargesymbols=\mathgroup13
Package bm Info: No bold for \U/msa/m/n, using \pmb.
Package bm Info: No bold for \U/msb/m/n, using \pmb.
LaTeX Font Info:    Redeclaring math alphabet \mathbf on input line 149.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip18
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/caption/subcaption.sty
Package: subcaption 2023/07/28 v1.6b Sub-captions (AR)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/caption/caption.sty
Package: caption 2023/08/05 v3.6o Customizing captions (AR)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/caption/caption3.sty
Package: caption3 2023/07/31 v2.4d caption3 kernel (AR)
\caption@tempdima=\dimen261
\captionmargin=\dimen262
\caption@leftmargin=\dimen263
\caption@rightmargin=\dimen264
\caption@width=\dimen265
\caption@indent=\dimen266
\caption@parindent=\dimen267
\caption@hangindent=\dimen268
Package caption Info: Unknown document class (or package),
(caption)             standard defaults will be used.
Package caption Info: \@makecaption = \long macro:#1#2->\captionstyle \ifx \@captype \fig@type \vskip \figcapgap \fi \setbox \@tempboxa \hbox {{\floatlegendstyle #1\floatcounterend }\capstrut #2}\ifdim \wd \@tempboxa >\hsize {\floatlegendstyle #1\floatcounterend }\capstrut #2\par \else \hbox to\hsize {\leftlegendglue \unhbox \@tempboxa \hfil }\fi \ifx \@captype \fig@type \else \vskip \tabcapgap \fi  on input line 1175.
)

Package caption Warning: Unknown document class (or package),
(caption)                standard defaults will be used.
See the caption package documentation for explanation.

\c@caption@flags=\count325
\c@continuedfloat=\count326
)
Package caption Info: New subtype `subfigure' on input line 238.
\c@subfigure=\count327
Package caption Info: New subtype `subtable' on input line 238.
\c@subtable=\count328
) (/usr/local/texlive/2025/texmf-dist/tex/latex/multirow/multirow.sty
Package: multirow 2024/11/12 v2.9 Span multiple rows of a table
\multirow@colwidth=\skip60
\multirow@cntb=\count329
\multirow@dima=\skip61
\bigstrutjot=\dimen269
) (/usr/local/texlive/2025/texmf-dist/tex/latex/tools/array.sty
Package: array 2024/10/17 v2.6g Tabular extension package (FMi)
\col@sep=\dimen270
\ar@mcellbox=\box104
\extrarowheight=\dimen271
\NC@list=\toks38
\extratabsurround=\skip62
\backup@length=\skip63
\ar@cellbox=\box105
)
\@indexfile=\write4
\openout4 = `author302.idx'.


Writing index file author302.idx
LaTeX Font Info:    Trying to load font information for OT1+ntxtlf on input line 34.
(/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/ot1ntxtlf.fd
File: ot1ntxtlf.fd 2021/05/24 v1.0 font definition file for OT1/ntx/tlf
)
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 10.0pt on input line 34.
 (./author302.aux)
\openout1 = `author302.aux'.

LaTeX Font Info:    Checking defaults for OML/ntxmi/m/it on input line 34.
LaTeX Font Info:    Trying to load font information for OML+ntxmi on input line 34.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/omlntxmi.fd
File: omlntxmi.fd 2015/08/25 Fontinst v1.933 font definitions for OML/ntxmi.
)
LaTeX Font Info:    ... okay on input line 34.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 34.
LaTeX Font Info:    ... okay on input line 34.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 34.
LaTeX Font Info:    ... okay on input line 34.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 34.
LaTeX Font Info:    ... okay on input line 34.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 34.
LaTeX Font Info:    ... okay on input line 34.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 34.
LaTeX Font Info:    ... okay on input line 34.
LaTeX Font Info:    Checking defaults for U/ntxexa/m/n on input line 34.
LaTeX Font Info:    Trying to load font information for U+ntxexa on input line 34.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/untxexa.fd
File: untxexa.fd 2012/04/16 Fontinst v1.933 font definitions for U/ntxexa.
)
LaTeX Font Info:    ... okay on input line 34.
LaTeX Font Info:    Checking defaults for LMS/ntxsy/m/n on input line 34.
LaTeX Font Info:    Trying to load font information for LMS+ntxsy on input line 34.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/lmsntxsy.fd
File: lmsntxsy.fd 2016/07/02 Fontinst v1.933 font definitions for LMS/ntxsy.
)
LaTeX Font Info:    ... okay on input line 34.
LaTeX Font Info:    Checking defaults for LMX/ntxexx/m/n on input line 34.
LaTeX Font Info:    Trying to load font information for LMX+ntxexx on input line 34.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/lmxntxexx.fd
File: lmxntxexx.fd 2016/07/03 Fontinst v1.933 font definitions for LMX/ntxexx.
)
LaTeX Font Info:    ... okay on input line 34.
 (/usr/local/texlive/2025/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count330
\scratchdimen=\dimen272
\scratchbox=\box106
\nofMPsegments=\count331
\nofMParguments=\count332
\everyMPshowfont=\toks39
\MPscratchCnt=\count333
\MPscratchDim=\dimen273
\MPnumerator=\count334
\makeMPintoPDFobject=\count335
\everyMPtoPDFconversion=\toks40
) (/usr/local/texlive/2025/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 485.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Live
))
\c@mv@tabular=\count336
\c@mv@boldtabular=\count337
LaTeX Info: Command `\dddot' is already robust on input line 34.
LaTeX Info: Command `\ddddot' is already robust on input line 34.
Package caption Info: Begin \AtBeginDocument code.
Package caption Info: End \AtBeginDocument code.
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 14.0pt on input line 49.
LaTeX Font Info:    Font shape `OT1/ntxtlf/b/n' will be
(Font)              scaled to size 14.0pt on input line 49.
LaTeX Font Info:    Font shape `OT1/ntxtlf/b/n' will be
(Font)              scaled to size 16.0pt on input line 49.
LaTeX Font Info:    Trying to load font information for OT1+minntx on input line 49.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/ot1minntx.fd
File: ot1minntx.fd 2023/09/09 v1.1 font definition file for OT1/minntx
)
LaTeX Font Info:    Font shape `OT1/minntx/m/n' will be
(Font)              scaled to size 10.0pt on input line 49.
LaTeX Font Info:    Font shape `OT1/minntx/m/n' will be
(Font)              scaled to size 7.3pt on input line 49.
LaTeX Font Info:    Font shape `OT1/minntx/m/n' will be
(Font)              scaled to size 5.5pt on input line 49.
LaTeX Font Info:    Font shape `OML/ntxmi/m/it' will be
(Font)              scaled to size 10.0pt on input line 49.
LaTeX Font Info:    Font shape `OML/ntxmi/m/it' will be
(Font)              scaled to size 7.3pt on input line 49.
LaTeX Font Info:    Font shape `OML/ntxmi/m/it' will be
(Font)              scaled to size 5.5pt on input line 49.
LaTeX Font Info:    Font shape `LMS/ntxsy/m/n' will be
(Font)              scaled to size 10.0pt on input line 49.
LaTeX Font Info:    Font shape `LMS/ntxsy/m/n' will be
(Font)              scaled to size 7.3pt on input line 49.
LaTeX Font Info:    Font shape `LMS/ntxsy/m/n' will be
(Font)              scaled to size 5.5pt on input line 49.
LaTeX Font Info:    Font shape `LMX/ntxexx/m/n' will be
(Font)              scaled to size 10.0pt on input line 49.
LaTeX Font Info:    Font shape `LMX/ntxexx/m/n' will be
(Font)              scaled to size 7.3pt on input line 49.
LaTeX Font Info:    Font shape `LMX/ntxexx/m/n' will be
(Font)              scaled to size 5.5pt on input line 49.
LaTeX Font Info:    Trying to load font information for U+ntxmia on input line 49.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/untxmia.fd
File: untxmia.fd 2024/04/09 Fontinst v1.933 font definitions for U/ntxmia.
)
LaTeX Font Info:    Font shape `U/ntxmia/m/it' will be
(Font)              scaled to size 10.0pt on input line 49.
LaTeX Font Info:    Font shape `U/ntxmia/m/it' will be
(Font)              scaled to size 7.3pt on input line 49.
LaTeX Font Info:    Font shape `U/ntxmia/m/it' will be
(Font)              scaled to size 5.5pt on input line 49.
LaTeX Font Info:    Trying to load font information for U+ntxsym on input line 49.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/untxsym.fd
File: untxsym.fd 2023/08/16 Fontinst v1.933 font definitions for U/ntxsym.
)
LaTeX Font Info:    Font shape `U/ntxsym/m/n' will be
(Font)              scaled to size 10.0pt on input line 49.
LaTeX Font Info:    Font shape `U/ntxsym/m/n' will be
(Font)              scaled to size 7.3pt on input line 49.
LaTeX Font Info:    Font shape `U/ntxsym/m/n' will be
(Font)              scaled to size 5.5pt on input line 49.
LaTeX Font Info:    Trying to load font information for U+ntxsyc on input line 49.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/untxsyc.fd
File: untxsyc.fd 2012/04/12 Fontinst v1.933 font definitions for U/ntxsyc.
)
LaTeX Font Info:    Font shape `U/ntxsyc/m/n' will be
(Font)              scaled to size 10.0pt on input line 49.
LaTeX Font Info:    Font shape `U/ntxsyc/m/n' will be
(Font)              scaled to size 7.3pt on input line 49.
LaTeX Font Info:    Font shape `U/ntxsyc/m/n' will be
(Font)              scaled to size 5.5pt on input line 49.
LaTeX Font Info:    Font shape `U/ntxexa/m/n' will be
(Font)              scaled to size 10.0pt on input line 49.
LaTeX Font Info:    Font shape `U/ntxexa/m/n' will be
(Font)              scaled to size 7.3pt on input line 49.
LaTeX Font Info:    Font shape `U/ntxexa/m/n' will be
(Font)              scaled to size 5.5pt on input line 49.
LaTeX Font Info:    Font shape `OT1/minntx/b/n' will be
(Font)              scaled to size 10.0pt on input line 49.
LaTeX Font Info:    Font shape `OT1/minntx/b/n' will be
(Font)              scaled to size 7.3pt on input line 49.
LaTeX Font Info:    Font shape `OT1/minntx/b/n' will be
(Font)              scaled to size 5.5pt on input line 49.
LaTeX Font Info:    Font shape `OML/ntxmi/b/it' will be
(Font)              scaled to size 10.0pt on input line 49.
LaTeX Font Info:    Font shape `OML/ntxmi/b/it' will be
(Font)              scaled to size 7.3pt on input line 49.
LaTeX Font Info:    Font shape `OML/ntxmi/b/it' will be
(Font)              scaled to size 5.5pt on input line 49.
LaTeX Font Info:    Font shape `LMS/ntxsy/b/n' will be
(Font)              scaled to size 10.0pt on input line 49.
LaTeX Font Info:    Font shape `LMS/ntxsy/b/n' will be
(Font)              scaled to size 7.3pt on input line 49.
LaTeX Font Info:    Font shape `LMS/ntxsy/b/n' will be
(Font)              scaled to size 5.5pt on input line 49.
LaTeX Font Info:    Font shape `LMX/ntxexx/b/n' will be
(Font)              scaled to size 10.0pt on input line 49.
LaTeX Font Info:    Font shape `LMX/ntxexx/b/n' will be
(Font)              scaled to size 7.3pt on input line 49.
LaTeX Font Info:    Font shape `LMX/ntxexx/b/n' will be
(Font)              scaled to size 5.5pt on input line 49.
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 8.5pt on input line 49.
LaTeX Font Info:    Calculating math sizes for size <8.5> on input line 49.
LaTeX Font Info:    Font shape `OT1/minntx/m/n' will be
(Font)              scaled to size 8.5pt on input line 49.
LaTeX Font Info:    Font shape `OT1/minntx/m/n' will be
(Font)              scaled to size 6.20496pt on input line 49.
LaTeX Font Info:    Font shape `OT1/minntx/m/n' will be
(Font)              scaled to size 4.67502pt on input line 49.
LaTeX Font Info:    Font shape `OML/ntxmi/m/it' will be
(Font)              scaled to size 8.5pt on input line 49.
LaTeX Font Info:    Font shape `OML/ntxmi/m/it' will be
(Font)              scaled to size 6.20496pt on input line 49.
LaTeX Font Info:    Font shape `OML/ntxmi/m/it' will be
(Font)              scaled to size 4.67502pt on input line 49.
LaTeX Font Info:    Font shape `LMS/ntxsy/m/n' will be
(Font)              scaled to size 8.5pt on input line 49.
LaTeX Font Info:    Font shape `LMS/ntxsy/m/n' will be
(Font)              scaled to size 6.20496pt on input line 49.
LaTeX Font Info:    Font shape `LMS/ntxsy/m/n' will be
(Font)              scaled to size 4.67502pt on input line 49.
LaTeX Font Info:    Font shape `LMX/ntxexx/m/n' will be
(Font)              scaled to size 8.5pt on input line 49.
LaTeX Font Info:    Font shape `LMX/ntxexx/m/n' will be
(Font)              scaled to size 6.20496pt on input line 49.
LaTeX Font Info:    Font shape `LMX/ntxexx/m/n' will be
(Font)              scaled to size 4.67502pt on input line 49.
LaTeX Font Info:    Font shape `U/ntxmia/m/it' will be
(Font)              scaled to size 8.5pt on input line 49.
LaTeX Font Info:    Font shape `U/ntxmia/m/it' will be
(Font)              scaled to size 6.20496pt on input line 49.
LaTeX Font Info:    Font shape `U/ntxmia/m/it' will be
(Font)              scaled to size 4.67502pt on input line 49.
LaTeX Font Info:    Font shape `U/ntxsym/m/n' will be
(Font)              scaled to size 8.5pt on input line 49.
LaTeX Font Info:    Font shape `U/ntxsym/m/n' will be
(Font)              scaled to size 6.20496pt on input line 49.
LaTeX Font Info:    Font shape `U/ntxsym/m/n' will be
(Font)              scaled to size 4.67502pt on input line 49.
LaTeX Font Info:    Font shape `U/ntxsyc/m/n' will be
(Font)              scaled to size 8.5pt on input line 49.
LaTeX Font Info:    Font shape `U/ntxsyc/m/n' will be
(Font)              scaled to size 6.20496pt on input line 49.
LaTeX Font Info:    Font shape `U/ntxsyc/m/n' will be
(Font)              scaled to size 4.67502pt on input line 49.
LaTeX Font Info:    Font shape `U/ntxexa/m/n' will be
(Font)              scaled to size 8.5pt on input line 49.
LaTeX Font Info:    Font shape `U/ntxexa/m/n' will be
(Font)              scaled to size 6.20496pt on input line 49.
LaTeX Font Info:    Font shape `U/ntxexa/m/n' will be
(Font)              scaled to size 4.67502pt on input line 49.
LaTeX Font Info:    Font shape `OT1/minntx/b/n' will be
(Font)              scaled to size 8.5pt on input line 49.
LaTeX Font Info:    Font shape `OT1/minntx/b/n' will be
(Font)              scaled to size 6.20496pt on input line 49.
LaTeX Font Info:    Font shape `OT1/minntx/b/n' will be
(Font)              scaled to size 4.67502pt on input line 49.
LaTeX Font Info:    Font shape `OML/ntxmi/b/it' will be
(Font)              scaled to size 8.5pt on input line 49.
LaTeX Font Info:    Font shape `OML/ntxmi/b/it' will be
(Font)              scaled to size 6.20496pt on input line 49.
LaTeX Font Info:    Font shape `OML/ntxmi/b/it' will be
(Font)              scaled to size 4.67502pt on input line 49.
LaTeX Font Info:    Font shape `LMS/ntxsy/b/n' will be
(Font)              scaled to size 8.5pt on input line 49.
LaTeX Font Info:    Font shape `LMS/ntxsy/b/n' will be
(Font)              scaled to size 6.20496pt on input line 49.
LaTeX Font Info:    Font shape `LMS/ntxsy/b/n' will be
(Font)              scaled to size 4.67502pt on input line 49.
LaTeX Font Info:    Font shape `LMX/ntxexx/b/n' will be
(Font)              scaled to size 8.5pt on input line 49.
LaTeX Font Info:    Font shape `LMX/ntxexx/b/n' will be
(Font)              scaled to size 6.20496pt on input line 49.
LaTeX Font Info:    Font shape `LMX/ntxexx/b/n' will be
(Font)              scaled to size 4.67502pt on input line 49.
LaTeX Font Info:    Trying to load font information for OT1+pcr on input line 49.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/psnfss/ot1pcr.fd
File: ot1pcr.fd 2001/06/04 font definitions for OT1/pcr.
)
LaTeX Font Info:    Font shape `OT1/ntxtlf/b/n' will be
(Font)              scaled to size 10.0pt on input line 51.
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 12.0pt on input line 56.
LaTeX Font Info:    Font shape `OT1/ntxtlf/b/n' will be
(Font)              scaled to size 12.0pt on input line 56.


[1



{/usr/local/texlive/2025/texmf-var/fonts/map/pdftex/updmap/pdftex.map}{/usr/local/texlive/2025/texmf-dist/fonts/enc/dvips/newtx/ntx-ot1-tlf.enc}{/usr/local/texlive/2025/texmf-dist/fonts/enc/dvips/base/8r.enc}]
<fig302/1.png, id=9, 699.048pt x 530.418pt>
File: fig302/1.png Graphic file (type png)
<use fig302/1.png>
Package pdftex.def Info: fig302/1.png  used on input line 64.
(pdftex.def)             Requested size: 299.60547pt x 227.33069pt.


[2 <./fig302/1.png>]
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 11.0pt on input line 75.
LaTeX Font Info:    Font shape `OT1/ntxtlf/b/n' will be
(Font)              scaled to size 11.0pt on input line 75.
<fig302/2.png, id=15, 5437.31375pt x 2476.25125pt>
File: fig302/2.png Graphic file (type png)
<use fig302/2.png>
Package pdftex.def Info: fig302/2.png  used on input line 81.
(pdftex.def)             Requested size: 299.60547pt x 136.43983pt.


[3 <./fig302/2.png>]
LaTeX Font Info:    Font shape `OT1/ntxtlf/b/n' will be
(Font)              scaled to size 8.5pt on input line 98.


[4]
<fig302/3a.png, id=23, 3308.36pt x 2484.28125pt>
File: fig302/3a.png Graphic file (type png)
<use fig302/3a.png>
Package pdftex.def Info: fig302/3a.png  used on input line 144.
(pdftex.def)             Requested size: 163.12146pt x 122.47762pt.
<fig302/3b.png, id=24, 1927.2pt x 1445.4pt>
File: fig302/3b.png Graphic file (type png)
<use fig302/3b.png>
Package pdftex.def Info: fig302/3b.png  used on input line 150.
(pdftex.def)             Requested size: 163.12146pt x 122.33908pt.


[5]
<fig302/4.png, id=28, 4456.65pt x 1449.415pt>
File: fig302/4.png Graphic file (type png)
<use fig302/4.png>
Package pdftex.def Info: fig302/4.png  used on input line 177.
(pdftex.def)             Requested size: 332.89723pt x 108.25911pt.


[6 <./fig302/3a.png> <./fig302/3b.png> <./fig302/4.png>]
<fig302/5a.png, id=33, 2484.28125pt x 2484.28125pt>
File: fig302/5a.png Graphic file (type png)
<use fig302/5a.png>
Package pdftex.def Info: fig302/5a.png  used on input line 204.
(pdftex.def)             Requested size: 106.52954pt x 106.51875pt.
<fig302/5b.png, id=34, 2484.28125pt x 2484.28125pt>
File: fig302/5b.png Graphic file (type png)
<use fig302/5b.png>
Package pdftex.def Info: fig302/5b.png  used on input line 211.
(pdftex.def)             Requested size: 106.52954pt x 106.51875pt.
<fig302/5c.png, id=35, 2484.28125pt x 2484.28125pt>
File: fig302/5c.png Graphic file (type png)
<use fig302/5c.png>
Package pdftex.def Info: fig302/5c.png  used on input line 218.
(pdftex.def)             Requested size: 106.52954pt x 106.51875pt.
<fig302/6a.jpg, id=36, 642.4pt x 642.4pt>
File: fig302/6a.jpg Graphic file (type jpg)
<use fig302/6a.jpg>
Package pdftex.def Info: fig302/6a.jpg  used on input line 230.
(pdftex.def)             Requested size: 106.52954pt x 106.52074pt.
<fig302/6b.jpg, id=37, 642.4pt x 642.4pt>
File: fig302/6b.jpg Graphic file (type jpg)
<use fig302/6b.jpg>
Package pdftex.def Info: fig302/6b.jpg  used on input line 237.
(pdftex.def)             Requested size: 106.52954pt x 106.52074pt.
<fig302/6c.jpg, id=38, 642.4pt x 642.4pt>
File: fig302/6c.jpg Graphic file (type jpg)
<use fig302/6c.jpg>
Package pdftex.def Info: fig302/6c.jpg  used on input line 244.
(pdftex.def)             Requested size: 106.52954pt x 106.52074pt.
<fig302/7a.png, id=39, 617.30624pt x 462.72874pt>
File: fig302/7a.png Graphic file (type png)
<use fig302/7a.png>
Package pdftex.def Info: fig302/7a.png  used on input line 255.
(pdftex.def)             Requested size: 163.12146pt x 122.2695pt.
<fig302/7b.png, id=40, 617.30624pt x 462.72874pt>
File: fig302/7b.png Graphic file (type png)
<use fig302/7b.png>
Package pdftex.def Info: fig302/7b.png  used on input line 260.
(pdftex.def)             Requested size: 163.12146pt x 122.2695pt.
<fig302/7c.png, id=41, 617.30624pt x 462.72874pt>
File: fig302/7c.png Graphic file (type png)
<use fig302/7c.png>
Package pdftex.def Info: fig302/7c.png  used on input line 266.
(pdftex.def)             Requested size: 163.12146pt x 122.2695pt.
<fig302/7d.png, id=42, 617.30624pt x 462.72874pt>
File: fig302/7d.png Graphic file (type png)
<use fig302/7d.png>
Package pdftex.def Info: fig302/7d.png  used on input line 271.
(pdftex.def)             Requested size: 163.12146pt x 122.2695pt.


[7]
<fig302/8.png, id=50, 4227.795pt x 1593.955pt>
File: fig302/8.png Graphic file (type png)
<use fig302/8.png>
Package pdftex.def Info: fig302/8.png  used on input line 298.
(pdftex.def)             Requested size: 332.89723pt x 125.50029pt.


[8 <./fig302/5a.png> <./fig302/5b.png> <./fig302/5c.png> <./fig302/6a.jpg> <./fig302/6b.jpg> <./fig302/6c.jpg>]

[9 <./fig302/7a.png> <./fig302/7b.png> <./fig302/7c.png> <./fig302/7d.png> <./fig302/8.png>]
<fig302/9.png, id=65, 2750.275pt x 2128.95375pt>
File: fig302/9.png Graphic file (type png)
<use fig302/9.png>
Package pdftex.def Info: fig302/9.png  used on input line 325.
(pdftex.def)             Requested size: 332.89723pt x 257.67242pt.


[10 <./fig302/9.png>]

[11]

[12]

[13]

[14]
<fig302/10a.png, id=83, 617.30624pt x 462.72874pt>
File: fig302/10a.png Graphic file (type png)
<use fig302/10a.png>
Package pdftex.def Info: fig302/10a.png  used on input line 465.
(pdftex.def)             Requested size: 159.78925pt x 119.77708pt.
<fig302/10b.png, id=84, 617.30624pt x 462.72874pt>
File: fig302/10b.png Graphic file (type png)
<use fig302/10b.png>
Package pdftex.def Info: fig302/10b.png  used on input line 471.
(pdftex.def)             Requested size: 159.78925pt x 119.77708pt.
<fig302/10c.png, id=85, 617.30624pt x 462.72874pt>
File: fig302/10c.png Graphic file (type png)
<use fig302/10c.png>
Package pdftex.def Info: fig302/10c.png  used on input line 477.
(pdftex.def)             Requested size: 159.78925pt x 119.77708pt.
<fig302/10d.png, id=86, 617.30624pt x 462.72874pt>
File: fig302/10d.png Graphic file (type png)
<use fig302/10d.png>
Package pdftex.def Info: fig302/10d.png  used on input line 483.
(pdftex.def)             Requested size: 159.78925pt x 119.77708pt.
<fig302/10e.png, id=87, 617.30624pt x 462.72874pt>
File: fig302/10e.png Graphic file (type png)
<use fig302/10e.png>
Package pdftex.def Info: fig302/10e.png  used on input line 489.
(pdftex.def)             Requested size: 159.78925pt x 119.77708pt.
<fig302/10f.png, id=88, 617.30624pt x 462.72874pt>
File: fig302/10f.png Graphic file (type png)
<use fig302/10f.png>
Package pdftex.def Info: fig302/10f.png  used on input line 495.
(pdftex.def)             Requested size: 159.78925pt x 119.77708pt.
<fig302/11a.png, id=89, 617.30624pt x 462.72874pt>
File: fig302/11a.png Graphic file (type png)
<use fig302/11a.png>
Package pdftex.def Info: fig302/11a.png  used on input line 507.
(pdftex.def)             Requested size: 106.52954pt x 79.84903pt.
<fig302/11b.png, id=90, 617.30624pt x 462.72874pt>
File: fig302/11b.png Graphic file (type png)
<use fig302/11b.png>
Package pdftex.def Info: fig302/11b.png  used on input line 513.
(pdftex.def)             Requested size: 106.52954pt x 79.84903pt.
<fig302/11c.png, id=91, 617.30624pt x 462.72874pt>
File: fig302/11c.png Graphic file (type png)
<use fig302/11c.png>
Package pdftex.def Info: fig302/11c.png  used on input line 519.
(pdftex.def)             Requested size: 106.52954pt x 79.84903pt.


[15 <./fig302/10a.png> <./fig302/10b.png> <./fig302/10c.png> <./fig302/10d.png> <./fig302/10e.png> <./fig302/10f.png>] (./references302.tex
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/it' will be
(Font)              scaled to size 8.5pt on input line 4.


[16 <./fig302/11a.png> <./fig302/11b.png> <./fig302/11c.png>]
Underfull \hbox (badness 10000) in paragraph at lines 40--41
[]\OT1/minntx/m/n/8.5 "Vital signs," \OT1/ntxtlf/m/it/8.5 Wikipedia, The Free En-cy-clo-pe-dia\OT1/minntx/m/n/8.5 . Avail-able on-line:
 []



[17])

[18] (./author302.aux)
 ***********
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2020/03/25>
 ***********
 ) 
Here is how much of TeX's memory you used:
 10490 strings out of 473190
 157574 string characters out of 5715800
 569123 words of memory out of 5000000
 33547 multiletter control sequences out of 15000+600000
 617416 words of font info for 153 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 83i,10n,131p,1235b,579s stack positions out of 10000i,1000n,20000p,200000b,200000s
</usr/local/texlive/2025/texmf-dist/fonts/type1/public/newtx/NewTXMI.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/newtx/txmiaX.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/newtx/txsys.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/urw/courier/ucrr8a.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/newtx/ztmb.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/newtx/ztmr.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/newtx/ztmri.pfb>
Output written on author302.pdf (18 pages, 12299987 bytes).
PDF statistics:
 147 PDF objects out of 1000 (max. 8388607)
 65 compressed objects within 1 object stream
 0 named destinations out of 1000 (max. 500000)
 131 words of extra memory for PDF output out of 10000 (max. 10000000)

