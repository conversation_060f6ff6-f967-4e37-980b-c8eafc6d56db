% RECOMMENDED %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\documentclass[graybox]{svmult}

% choose options for [] as required from the list
% in the Reference Guide

\usepackage{mathptmx}       % selects Times Roman as basic font
\usepackage{helvet}         % selects Helvetica as sans-serif font
\usepackage{courier}        % selects <PERSON><PERSON> as typewriter font
\usepackage{type1cm}        % activate if the above 3 fonts are
                            % not available on your system

\usepackage{makeidx}         % allows index generation
\usepackage{graphicx}        % standard LaTeX graphics tool
                            % when including figure files
\usepackage{multicol}        % used for the two-column index
\usepackage[bottom]{footmisc}% places footnotes at page bottom
\usepackage{amsmath}         % For math environments
\usepackage{amsfonts}        % For math fonts
\usepackage{amssymb}         % For math symbols
\usepackage{url}             % For \url command
\usepackage{subcaption}      % For subfigures
\usepackage{multirow}        % For multirow cells in tables

% see the list of further useful packages
% in the Reference Guide

\makeindex             % used for the subject index
                      % please use the style svind.ist with
                      % your makeindex program

\begin{document}

\title{A High Precision Symptom Prediction and Diagnosis of Atrial Fibrillation Using CNN and LSTM with Multimodal Feature Fusion Technique}
% Use \titlerunning{Short Title} for an abbreviated version of
% your contribution title if the original one is too long
\titlerunning{High Precision AF Prediction Using CNN and LSTM}
\author{Wei-Hsiu Tseng, Tse-Hsien Lu, Chung-Lieh Hung, and Che-Lun Hung}
% Use \authorrunning{Short Title} for an abbreviated version of
% your contribution title if the original one is too long
\authorrunning{W-H. Tseng, T-H. Lu, C-L. Hung, and C-L. Hung}
\institute{Wei-Hsiu Tseng \at Institute of Biomedical Informatics, National Yang Ming Chiao Tung University, Taipei, Taiwan, \email{<EMAIL>}
\and Tse-Hsien Lu \at Institute of Biomedical Informatics, National Yang Ming Chiao Tung University, Taipei, Taiwan, \email{<EMAIL>}
\and Chung-Lieh Hung \at Division of Cardiology, Departments of Internal Medicine, MacKay Memorial Hospital, Taipei, Taiwan, \email{<EMAIL>}
\and Che-Lun Hung \at Institute of Biomedical Informatics, National Yang Ming Chiao Tung University, Taipei, Taiwan, \email{<EMAIL>}}

\maketitle

\abstract{Atrial fibrillation (AF) is the most common arrhythmia caused by irregular impulses in the atrial tissues, and it can potentially lead to stroke and impaired heart function in patients. Diagnosing this disease in the early stages is crucial to prevent its progression and improve the quality of life for patients. This study provides a comprehensive solution based on a multimodal model and a novel feature fusion framework to provide medical diagnosis, prediction, and early prevention capabilities for AF. The proposed multimodal model diagnoses possible AF symptoms using the features fused from signal and image sources extracted by neural network models. The training data includes electrocardiography and photoplethysmography data obtained from the MIMIC PERform AF dataset and the cardiac ultrasound images provided by collaborating hospitals and represent clinical medical images. The experimental results indicate that the proposed method achieved better classification performance than deep learning features alone, with accuracy, sensitivity, and specificity of 99.32\%, 99.98\%, and 98.61\%, respectively.}

\keywords{Multimodal, Deep Learning, Heart Failure, Cardiac Ultrasound, Signal analysis, Image Segmentation}


\section{Introduction}
\label{sec:1}
Cardiovascular diseases are among the leading causes of death worldwide [1]. Arrhythmias, characterized by irregular heart rhythms, are significant diseases, with Atrial Fibrillation (AF) being the most common [2, 3]. Without prompt medical care, AF can lead to cerebral hypoxia, causing anything from minor strokes to fatal outcomes. Efficient and accurate AF diagnosis is thus crucial for improving patient quality of life. Despite various detection techniques, conventional practices rely on dynamic ECG monitors and event recorders [4]. AF often manifests without symptoms [5], making it challenging for a single tool to detect cardiovascular diseases effectively. Hence, there is significant interest in using multimodal data sources for diagnosis [6, 7].

Most cardiac diseases are linked to physiological electrical signals from heart activities. ECG and PPG are well-established for automated cardiac detection [8--12]. To assess patients more precisely, clinicians use five vital signs: pulse rate, respiration rate, blood pressure, body temperature, and oxygen saturation [13]. Collecting data from various sources is now typical for disease diagnosis. However, AF is still hard to detect with source signals [14, 15]. Thus, we propose incorporating ultrasound images, extracting image features, and transforming them into numerical data to train a multimodal model, enhancing accuracy and reliability.

\begin{figure}[!t]
    \centering
    \includegraphics[width=0.9\linewidth]{fig302/Fig. 1. The time relationship chart between ECG and PPG.png}
    \caption{The time relationship chart between ECG and PPG illustrates the temporal correlation between ECG and PPG peaks.}
    \label{fig:1}
\end{figure}

\section{Related Works}
\label{sec:2}
Recent research has shown significant advancements in applying deep learning to cardiovascular disease prediction. Studies have proposed intelligent healthcare systems combining sensor data and electronic medical records, achieving high accuracy rates, such as 98.5\% for heart disease and 96.67\% for coronary artery disease (CAD) [16, 18]. Multimodal approaches integrating CT images, ECG, PCG, and MRI data have also been developed, improving risk assessment for ischemic heart disease (IHD) and CAD [17, 19].

\section{Methods}
\label{sec:3}
\subsection{Framework}
\label{subsec:3.1}
The following diagram illustrates the complete workflow of the research process. It can be observed that signal and image sources are processed with different models to extract their respective features. In the case of image sources, a segmentation step is applied to obtain mask values for the chambers. Then, both features were fused and fed into a multilayer perceptron for classification, and the classifier model generated the final prediction results.

\begin{figure*}[!t]
    \centering
    \includegraphics[width=0.9\textwidth]{fig302/Fig. 2. The multi-modal model architecture for predicting AF.png}
    \caption{The multi-modal model architecture for predicting AF.}
    \label{fig:2}
\end{figure*}

The inclusion of ablation experiments is separate from the diagram. The study also compared the classification performance of individual models and the classification performance of data before and after fusion using the same MLP model. The results will be discussed in Chapter Four.

\subsection{Clinical database collection}
\subsubsection{Signals}
In line with previous works in the literature [20], 35 pairs of ECG and PPG data from 35 critically ill adults during routine clinical care were obtained and extracted from the MIMIC-III Waveform Database Matched Subset [21] called MIMIC PERform AF Dataset. Nineteen of these were acquired during AF, and the rest were acquired during normal sinus rhythm, as described in [22]. Each data pair contains a 20-minute duration of synchronous ECG and PPG recordings.

\begin{table}[!t]
\caption{Dataset prescription}
\label{tab:dataset_prescription}
\centering
\begin{tabular}{p{2.2cm}p{1.8cm}p{2.2cm}p{2.2cm}p{2.2cm}}
\hline\noalign{\smallskip}
\textbf{Dataset} & \textbf{Subject} & \textbf{Class} & \textbf{Equipment} & \textbf{Total beat} \\
\noalign{\smallskip}\hline\noalign{\smallskip}
MIMIC PERform AF Dataset & 35 critically-ill adults & 2 classes AF:19 Non-AF:16 & Bedside monitor at 125Hz & AF: 29,592 Non-AF: 22,477 \\
\noalign{\smallskip}\hline\noalign{\smallskip}
\end{tabular}
\end{table}

\subsubsection{Cardiac ultrasound images}
The cardiac Digital Imaging and Communications in Medicine (DICOM) files were received from the Division of Cardiology, Department of Internal Medicine, MacKay Memorial Hospital in Taipei, Taiwan. The dataset includes 507 patients with Apical Four-Chamber (A4C) views and 501 patients with Apical Two-Chamber (A2C) views, which are used for training segmentation and detection models for the left atrium and left ventricle. It comprises 324 patients with A4C views for training segmentation and detection models for the right ventricle to extract numerical features. All DICOM files have undergone a second verification by medical professionals.

\begin{table}[!t]
\caption{The quantity of patients and images employed in the detection and segmentation models}
\label{tab:quantity_patients_images}
\centering
\begin{tabular}{cc|ccc}
\hline\noalign{\smallskip}
& & \textbf{LA} & \textbf{LV} & \textbf{RV} \\
\noalign{\smallskip}\hline\noalign{\smallskip}
\multirow{2}{*}{A2C} & Patient & 501 & 501 & - \\
& Image & 501 & 501 & - \\
\noalign{\smallskip}\hline\noalign{\smallskip}
\multirow{2}{*}{A4C} & Patient & 507 & 507 & 324 \\
& Image & 507 & 507 & 648 \\
\noalign{\smallskip}\hline\noalign{\smallskip}
\end{tabular}
\end{table}

\subsection{Signal data preprocessing}
Five main stages were included in this section: (1) Signal combination, (2) Signal segmentation, (3) Resampling, (4) Noise reduction, and (5) feature extraction.

\subsubsection{Signal combination}
MIMIC PERform AF Dataset contains ECG and PPG records measured simultaneously from the same subjects. Therefore, there are no issues with differences in time or sampling frequencies between different signals. This allows the two signal types to be combined and treated as the same input data in subsequent analysis steps.

\subsubsection{Signal segmentation}
In the settings we experimented with, segmenting the signal into input sequences of 10 seconds each produced the best results and conformed to clinical interpretation standards. When segmenting the data, we used a sliding window with a duration of t=10 seconds to cover all possible segments, ensuring that the same heartbeat does not appear in multiple segments. Each signal contains 1250 sampling points, with 2276 samples for the AF class and 1915 samples for the non-AF class.

\subsubsection{Resampling}
To ensure that the sampling frequency of the signal matches the sampling frequency of the filter, we performed oversampling before equalization to avoid incomplete spectrum display after the signal passes through the filter. Additionally, when the signal's sampling frequency matches the filter's sampling frequency, a higher sampling frequency results in better filtering; in this case, standard interpolation methods are used to resample the signal on a regular time axis at the given frequency.

\subsubsection{Noise reduction}
Currently, signal processing for ECG preprocessing extensively uses low-pass, band-pass, averaging, median filters, and wavelet transforms. However, band-pass filtering has limitations, such as an inability to track time-varying ECG features and fixed cutoff frequencies that can distort ST and QRS segments. Baseline drift is also a critical issue for ECG signals. This study tests four denoising methods: baseline wander filter, Butterworth filter, and band-pass filter, with results significantly favoring the Butterworth filter approach.

\begin{figure}[!t]
    \centering
    \begin{subfigure}{0.49\linewidth}
        \centering
        \includegraphics[width=\linewidth]{fig302/Fig. 3. (a) ECG.png}
        \caption{ECG}
        \label{fig:3a}
    \end{subfigure}
    \begin{subfigure}{0.49\linewidth}
        \centering
        \includegraphics[width=\linewidth]{fig302/Fig. 3. (b) PPG.png}
        \caption{PPG}
        \label{fig:3b}
    \end{subfigure}
    \caption{Comparison figure of ECG and PPG before and after noise reduction.}
    \label{fig:3}
\end{figure}

\subsubsection{Feature extraction}
The original ECG and PPG signals have a time length of 1200 seconds and a frequency of 125 Hz. They were resampled to 10 seconds for each record, resulting in 1250 data points as input for the model. The network for extracting signal features is depicted in Table III. It includes a Convolutional Neural Network (CNN) and a Long Short-Term Memory network (LSTM) and is thus named Sig-CLNet.

\begin{table}[!t]
\caption{The detailed parameters of Sig-CLNet}
\label{tab:sig_clnet_params}
\centering
\begin{tabular}{lc}
\hline\noalign{\smallskip}
Layers & 19 \\
Total params/Memory & 97601/381.25KB \\
Trainable params/Memory & 97153/379.50KB \\
Non-trainable params/Memory & 448/1.75KB \\
\noalign{\smallskip}\hline\noalign{\smallskip}
\end{tabular}
\end{table}

\begin{figure}[!t]
    \centering
    \includegraphics[width=\linewidth]{fig302/Fig. 4 Sig-CLNet architecture diagram.png}
    \caption{Sig-CLNet architecture diagram.}
    \label{fig:4}
\end{figure}

One-dimensional convolutional layers slide along the time axis of the input signals to extract meaningful features. Local features and patterns can be extracted from the signal data by increasing the filter size and using the Rectified Linear Unit (ReLU) activation function. After each convolutional layer, a max-pooling layer is added to reduce the spatial dimension while retaining essential features. Following each convolutional layer, there is a batch normalization layer and a ReLU to standardize the dispersed data, optimizing the neural network to make it easier for machine learning to discover patterns in the data. ReLU may mitigate the vanishing gradient problem.

Following the CNN layer, three LSTM layers were employed. LSTM can learn dependencies in time series data and was designed to address long-term dependency issues. In this study, the input sizes for the LSTM units were set to 32, 16, and 8, respectively. The output of LSTM2 contains deep encoded features for both ECG and PPG signals.

\subsection{Cardiac ultrasound images preprocessing}
\subsubsection{Image detection and segmentation}
The YOLOv8 model excels in ventricle detection and segmentation by using an Anchor-Free method, offering superior accuracy and speed over traditional Anchor-Based methods. Its architecture includes two main components: feature extraction with CSPDarknet, which enhances efficiency using a Cross Stage Partial structure, and object detection with the YOLOv4-Head, which processes feature maps into detection results. By directly predicting target center points and aspect ratios, YOLOv8 reduces anchor boxes, enhancing detection speed and accuracy and ensuring high performance in ventricle detection and segmentation tasks.

The segmentation mask is extracted from the YOLOv8 model. First, using the computer vision library OpenCV, the minimum bounding rectangle of the contours of the chamber segmentation mask is detected. Then, with the top-left corner of the rectangle as the reference, the length and width of each minimum bounding rectangle are calculated, and the ellipse of the minimum bounding rectangle is computed using the following formula:
\begin{equation}
S = \pi \times a \times b \label{eq:1}
\end{equation}
\begin{equation}
V = \frac{4}{3} \times \pi \times a \times b \times c \label{eq:2}
\end{equation}

These functions obtain the minimum bounding rectangle's area and volume and save a series of extracted continuous data, including the length, width, area, and volume of the minimum bounding rectangles, as a CSV comma-separated text file. Figure 7 illustrates the length, width, area, and volume values mentioned above.

\begin{figure}[!t]
    \centering
    \begin{subfigure}{0.32\linewidth}
        \centering
        \includegraphics[width=\linewidth]{fig302/Fig. 5. (a) A2CA4C Detection.png}
        \caption{A2C/A4C Detection}
        \label{fig:5a}
    \end{subfigure}
    \hfill
    \begin{subfigure}{0.32\linewidth}
        \centering
        \includegraphics[width=\linewidth]{fig302/Fig. 5. (b) LALV Segmentation.png}
        \caption{LA/LV Segmentation}
        \label{fig:5b}
    \end{subfigure}
    \hfill
    \begin{subfigure}{0.32\linewidth}
        \centering
        \includegraphics[width=\linewidth]{fig302/Fig. 5. (c) RV Segmentation.png}
        \caption{RV Segmentation}
        \label{fig:5c}
    \end{subfigure}
    \caption{The outcomes derived from the YOLOv8 models, which have been extensively trained for echocardiography in both detection and segmentation tasks. (a) Illustrates the chamber detection outcomes for the A4C view, while (b) and (c) showcase the results for the detection and segmentation of LA, LV, and RV}
    \label{fig:5}
\end{figure}

\begin{figure}[!t]
    \centering
    \begin{subfigure}{0.32\linewidth}
        \centering
        \includegraphics[width=\linewidth]{fig302/Fig. 6. (a) LA Segmentation.jpg}
        \caption{LA Segmentation}
        \label{fig:6a}
    \end{subfigure}
    \hfill
    \begin{subfigure}{0.32\linewidth}
        \centering
        \includegraphics[width=\linewidth]{fig302/Fig. 6. (b) LV Segmentation.jpg}
        \caption{LV Segmentation}
        \label{fig:6b}
    \end{subfigure}
    \hfill
    \begin{subfigure}{0.32\linewidth}
        \centering
        \includegraphics[width=\linewidth]{fig302/Fig. 6. (c) RV Segmentation.jpg}
        \caption{RV Segmentation}
        \label{fig:6c}
    \end{subfigure}
    \caption{The derived masks from the echocardiography detection and segmentation tasks. (a) and (b) correspond to the masks presented in Fig. 5 (b), while (c) aligns with the mask in Fig. 5 (c). The green box illustrates the minimum bounding rectangle of the mask, and the red line indicates its length or width}
    \label{fig:6}
\end{figure}

\begin{figure}[!t]
    \centering
    \begin{subfigure}{0.49\linewidth}
        \includegraphics[width=\linewidth]{fig302/Fig. 7. (a) Area.png}
        \caption{Area}
        \label{fig:7a}
    \end{subfigure}
    \begin{subfigure}{0.49\linewidth}
        \includegraphics[width=\linewidth]{fig302/Fig. 7. (b) Length.png}
        \caption{Length}
        \label{fig:7b}
    \end{subfigure}
    \\
    \begin{subfigure}{0.49\linewidth}
        \includegraphics[width=\linewidth]{fig302/Fig. 7. (c) Volume.png}
        \caption{Volume}
        \label{fig:7c}
    \end{subfigure}
    \begin{subfigure}{0.49\linewidth}
        \includegraphics[width=\linewidth]{fig302/Fig. 7. (d) Width.png}
        \caption{Width}
        \label{fig:7d}
    \end{subfigure}
    \caption{The information obtained from the segmentation masks is utilized to compute continuous data across a sequence of DICOM slices via Algorithm 2.}
    \label{fig:7}
\end{figure}

\subsubsection{Feature extraction}
This feature extraction model is named Ultra-CNet. The model architecture is a simple CNN structure that treats the numerical features from segmented cardiac ultrasound images as a one-dimensional array. Hence, the model employs 1DCNN for feature extraction, comprising four layers, incorporating a max-pooling layer to reduce spatial dimensions while preserving essential features.

\begin{table}[!t]
\caption{The detailed parameters of Ultra-CNet}
\label{tab:ultra_cnet_params}
\centering
\begin{tabular}{lc}
\hline\noalign{\smallskip}
Layers & 12 \\
Total params/Memory & 8113/31.69KB \\
Trainable params/Memory & 8113/31.69KB \\
Non-trainable params/Memory & 0/0.00KB \\
\noalign{\smallskip}\hline\noalign{\smallskip}
\end{tabular}
\end{table}

\begin{figure}[!t]
    \centering
    \includegraphics[width=\linewidth]{fig302/Fig. 8. Ultra-CNet architecture diagram.png}
    \caption{Ultra-CNet architecture diagram.}
    \label{fig:8}
\end{figure}

\subsection{Feature fusion}
The signals and images each obtain feature vectors of size 3352x32 and 90x64 through the previous feature extraction steps, respectively. These feature maps are then concatenated. After passing through the fusion layer, a fused feature matrix of size 3352x96 is obtained.

\subsection{Classification algorithm}
The fused feature subset will be input into an MLP classifier to perform the final AF prediction. This classifier comprises three layers, all composed of dense layers. Detailed parameter information can be found in Table V. The first two hidden layers are set to 64 and 32 units using the ReLU activation function. The output layer employs the sigmoid activation function to present binary classification results.

\begin{table}[!t]
\caption{The detailed parameters of the final MLP classifier}
\label{tab:mlp_params}
\centering
\begin{tabular}{lc}
\hline\noalign{\smallskip}
Layers & 3 \\
Total params/Memory & 8321/32.50KB \\
Trainable params/Memory & 8321/32.50KB \\
Non-trainable params/Memory & 0/0.00KB \\
\noalign{\smallskip}\hline\noalign{\smallskip}
\end{tabular}
\end{table}

\begin{figure}[!t]
    \centering
    \includegraphics[width=\linewidth]{fig302/Fig. 9. The final MLP architecture diagram.png}
    \caption{The final MLP architecture diagram.}
    \label{fig:9}
\end{figure}

\subsection{Evaluation criteria}
To quantify the classification results, we employed seven evaluation metrics: Accuracy (ACC), Sensitivity (SN), Specificity (SP), Precision (P), and F1-Score for performance comparison among cases. Their definitions are as follows:
\begin{equation}
ACC = \frac{TN + TP}{TN+FP + FN + TP} \label{eq:3}
\end{equation}
\begin{equation}
SN = \frac{TP}{FN + TP} \label{eq:4}
\end{equation}
\begin{equation}
SP = \frac{TN}{TN + FP} \label{eq:5}
\end{equation}
\begin{equation}
P = \frac{TP}{TP + FP} \label{eq:6}
\end{equation}
\begin{equation}
F1 - Score = \frac{2 \times P \times SN}{P + SN} \label{eq:7}
\end{equation}
TP, FN, FP, and TN represent the quantities of True Positives, False Negatives, False Positives, and True Negatives, respectively.

\section{Experiments}
\label{sec:4}
The abovementioned methods will be applied to the dataset, where the trained Sig-CLNet and Ultra-CNet will also be used to predict AF, demonstrating the advantages of the proposed multimodal feature fusion algorithm. Each network undergoes validation to compare each performance.

\subsection{Cardiac ultrasound image segmentation tool}
During the training phase, the mean average precision (mAP50) for YOLOv5, YOLOv7, and YOLOv8 models is evaluated using a standard threshold for Intersection over Union (IoU) score of 0.5. YOLOv8 demonstrates faster convergence than the other models, achieving its best performance at the 24th epoch with an mAP50 of 0.98892. In contrast, YOLOv5 and YOLOv7 achieved their best performances at the 27th and 28th epochs, respectively, with mAP50 scores of 0.88830 and 0.8802.

\subsection{Training setting of feature extraction network}
Both Sig-CLNet and Ultra-CNet use the Adam optimizer to update network parameters. Sig-CLNet networks run for 500 epochs while Ultra-CNet runs for 55 epochs throughout the training process, with an initialing learning rate of 0.0001 and batch size set to 32.

\subsection{Multi-domain classifier setting}
The classifier also utilizes the Adam optimizer to update iteration information and employs the binary\_crossentropy loss function to calculate training errors during the training process. The initial learning rate is 0.001, and the batch size is 32. The training process involves iterating through 10 epochs, allowing the neural network to reach its optimal fitting state.

\section{Results and Discussion}
\label{sec:5}
\subsection{Image detection}
As mentioned in Table II, the training dataset consists of 80\%, and the testing dataset comprises 20\%. The model achieves an accuracy of 85\%. Table VI details the performance scores for A2C and A4C in ventricle detection, with misclassification rates (MR) for A2C and A4C below 1\%. This indicates that almost all cardiac ventricle slices in DICOM can be accurately detected. Additionally, this study determines whether a DICOM file corresponds to an A2C view or an A4C view based on a condition predicting over half of the total slices and selects the appropriate atrium/ventricle segmentation model for A2C or A4C views.

\begin{table}[!t]
\caption{The detection report for chamber detection}
\label{tab:chamber_detection_report}
\centering
\begin{tabular}{lccccc}
\hline\noalign{\smallskip}
& \textbf{P} & \textbf{SN} & \textbf{SP} & \textbf{F1-score} & \textbf{MR} \\
\noalign{\smallskip}\hline\noalign{\smallskip}
A2C & 0.79 & 0.85 & 0.79 & 0.82 & 0.009 \\
A4C & 0.89 & 0.85 & 0.89 & 0.87 & 0.006 \\
\noalign{\smallskip}\hline\noalign{\smallskip}
ACC & \multicolumn{4}{c}{0.85} & \\
\noalign{\smallskip}\hline\noalign{\smallskip}
\end{tabular}
\end{table}



\subsection{Image segmentation}
As presented in Table II, the training dataset consists of 80\%, and the testing dataset comprises 20\%. Table VII reveals the MR for LA, LV, and RV segmentation, which indicates that 95\% of LA, 99\% of LV, and 67\% of RV in DICOM images can be accurately segmented.

\begin{table}[!t]
\caption{The misclassification rate of atrium/ventricle segmentation}
\label{tab:misclassification_rate}
\centering
\begin{tabular}{lc}
\hline\noalign{\smallskip}
& \textbf{MR} \\
\noalign{\smallskip}\hline\noalign{\smallskip}
LA & 0.05 \\
LV & 0.01 \\
RV & 0.33 \\
\noalign{\smallskip}\hline\noalign{\smallskip}
\end{tabular}
\end{table}

\subsection{Classification performance}
Table VIII presents the classification performance scores of each model in the performance metrics. Overall, Sig-CLNet stands out with an ACC of 0.94, SN of 0.89, SP and P at 0.99, and F1-Score of 0.94. However, the performance of Ultra-CNet could be improved. While the ACC and SP scores are 0.83 and 0.925, respectively, the other performance metrics are around 0.5.

While the performance of deep learning features alone is unstable, with significant variations in different indicators, this emphasizes the model's effectiveness in predicting AF after feature fusion. The final MLP classifier achieved excellent classification performance, as shown in Table VIII, with scores approaching 0.99 for each indicator.

\begin{table}[!t]
\caption{Performance metrics of models}
\label{tab:performance_metrics_models}
\centering
\begin{tabular}{lccccc}
\hline\noalign{\smallskip}
& \textbf{ACC} & \textbf{SN} & \textbf{SP} & \textbf{P} & \textbf{F1-Score} \\
\noalign{\smallskip}\hline\noalign{\smallskip}
Sig-CLNet & 0.943 & 0.89 & 0.99 & 0.99 & 0.94 \\
Ultra-CNet & 0.833 & 0.5 & 0.925 & 0.5 & 0.5 \\
MLP classifier & 0.993 & 0.999 & 0.986 & 0.99 & 0.993 \\
\noalign{\smallskip}\hline\noalign{\smallskip}
\end{tabular}
\end{table}

\begin{table}[!t]
\caption{Unified comparison using the MLP model}
\label{tab:unified_comparison_mlp}
\centering
\begin{tabular}{p{3cm}ccccc}
\hline\noalign{\smallskip}
& \textbf{ACC} & \textbf{SN} & \textbf{SP} & \textbf{P} & \textbf{F1-Score} \\
\noalign{\smallskip}\hline\noalign{\smallskip}
Signal data prediction & 0.86 & 0.886 & 0.833 & 0.846 & 0.866 \\
Image data prediction & 0.8 & 0.5 & 0.9 & 0.5 & 0.5 \\
\noalign{\smallskip}\hline\noalign{\smallskip}
Final multimodal prediction & 0.993 & 0.999 & 0.986 & 0.99 & 0.993 \\
\noalign{\smallskip}\hline\noalign{\smallskip}
\end{tabular}
\end{table}

Comparing the results with previously mentioned multimodal model studies, an intelligent healthcare system combining portable medical sensor (PMS) data and electronic medical records (EMR) achieved an accuracy of 98.5\% for heart disease prediction [16], while another study on CAD detection reached an accuracy of 96.67\% [18]. Additionally, previous multimodal approaches using CT and MRI data have shown improved risk assessment capabilities for IHD and CAD [17, 19]. In this study, the developed model that integrates ECG, PPG, and CUS achieved a higher accuracy of 99.3\%, indicating enhanced performance in detecting cardiac diseases and incorporating advanced feature selection and deep learning techniques, not only meeting these benchmarks but also surpassing them in some aspects, demonstrating the robustness and clinical potential of our approach.

\begin{table}[!t]
\caption{Evaluation of multimodal approaches for cardiac disease detection}
\label{tab:multimodal_approaches_eval}
\centering
\begin{tabular}{p{4cm}p{2.5cm}p{3cm}p{3cm}c}
\hline\noalign{\smallskip}
\textbf{Research objectives} & \textbf{Data} & \textbf{Algorithms/Models} & \textbf{Performance(\%)} & \textbf{Year} \\
\noalign{\smallskip}\hline\noalign{\smallskip}
Automated Cardiovascular Disease Detection [16] & PMS EMR & Ensemble deep learning & ACC: 82.5 P: 84.5 F1-score: 83.5 & 2020 \\
Risk Assessment for Ischemic Heart Disease [17] & CT EMR & XGBoost, machine learning & AUROC: 0.86 AUCPR: 0.70 & 2023 \\
Detection of Acute Coronary Syndrome [18] & ECG PCG CUS & SVM & ACC: 96.7 SN: 96.7 SP: 96.7 F1-score: 96.6 & 2020 \\
Comprehensive Non-Invasive Diagnosis of Coronary Artery Disease [19] & CT 3D MRI & FCN & ACC: 75 & 2020 \\
\noalign{\smallskip}\hline\noalign{\smallskip}
Published research & ECG PPG CUS & CNN+LSTM+MLP & ACC: 99.3 SN: 99.9 SP: 98.6 P: 99 F1-score: 99.3 & 2024 \\
\noalign{\smallskip}\hline\noalign{\smallskip}
\end{tabular}
\end{table}

\subsection{Analysis of models}
Figure 10 shows the loss and accuracy curves across iterations for Sig-CLNet, Ultra-CNet, and the MLP classifier to illustrate the generalization performance of each network. It can be observed that the loss and accuracy curves for the training dataset of all models indicate thorough model training. The accuracy trend of the validation data for Sig-CLNet closely mirrors the accuracy trend of the training data, demonstrating substantial convergence and good performance. However, in the case of Ultra-CNet, the accuracy trend for the validation data significantly deviates from that for the training data, and both models exhibit noticeable fluctuations. In the final MLP classifier, the loss and accuracy curves in the training dataset exhibit a rapid and smooth convergence, reaching optimal learning conditions after only ten iterations. No apparent oscillation during the training process indicates the absence of overfitting. Figure 11 illustrates the confusion matrices for Sig-CLNet, Ultra-CNet, and the final MLP classifier.

\begin{figure}[!t]
    \centering
    \begin{subfigure}{0.48\linewidth}
        \includegraphics[width=\linewidth]{fig302/Fig. 10. (a) Sig-CLNet accuracy.png}
        \caption{Sig-CLNet accuracy}
        \label{fig:10a}
    \end{subfigure}
    \hfill
    \begin{subfigure}{0.48\linewidth}
        \includegraphics[width=\linewidth]{fig302/Fig. 10. (b) Sig-CLNet loss.png}
        \caption{Sig-CLNet loss}
        \label{fig:10b}
    \end{subfigure}
    \\[1em]
    \begin{subfigure}{0.48\linewidth}
        \includegraphics[width=\linewidth]{fig302/Fig. 10. (c) Ultra-CNet accuracy.png}
        \caption{Ultra-CNet accuracy}
        \label{fig:10c}
    \end{subfigure}
    \hfill
    \begin{subfigure}{0.48\linewidth}
        \includegraphics[width=\linewidth]{fig302/Fig. 10. (d) Ultra-CNet loss.png}
        \caption{Ultra-CNet loss}
        \label{fig:10d}
    \end{subfigure}
    \\[1em]
    \begin{subfigure}{0.48\linewidth}
        \includegraphics[width=\linewidth]{fig302/Fig. 10. (e) The final MLP classifier accuracy.png}
        \caption{The final MLP classifier accuracy}
        \label{fig:10e}
    \end{subfigure}
    \hfill
    \begin{subfigure}{0.48\linewidth}
        \includegraphics[width=\linewidth]{fig302/Fig. 10. (f) The final MLP classifier loss.png}
        \caption{The final MLP classifier loss}
        \label{fig:10f}
    \end{subfigure}
    \caption{The training/validation loss and accuracy versus iteration plots for Sig-CLNet, Ultra-CNet, and multi-domain classifier}
    \label{fig:10}
\end{figure}


\begin{figure}[!t] % Figure 11
    \centering
    \begin{subfigure}{0.32\linewidth}
        \includegraphics[width=\linewidth]{fig302/Fig. 11. (a) Sig-CLNet confusion matrix.png}
        \caption{Sig-CLNet}
        \label{fig:11a}
    \end{subfigure}
    \hfill
    \begin{subfigure}{0.32\linewidth}
        \includegraphics[width=\linewidth]{fig302/Fig. 11. (b) Ultra-CNet confusion matrix.png}
        \caption{Ultra-CNet}
        \label{fig:11b}
    \end{subfigure}
    \hfill
    \begin{subfigure}{0.32\linewidth}
        \includegraphics[width=\linewidth]{fig302/Fig. 11. (c) The final MLP classifier confusion matrix.png}
        \caption{The final MLP classifier}
        \label{fig:11c}
    \end{subfigure}
    \caption{Confusion matrix of all models.}
    \label{fig:11}
\end{figure}


\section{Conclusions}
\label{sec:6}
This study presents a novel multimodal deep learning approach for AF prediction, integrating ECG, PPG, and cardiac ultrasound images. Preprocessing involves signal segmentation, noise reduction, and numerical extraction from ultrasound images with expert AF annotation. Sig-CLNet extracts signal-encoded features from ECG and PPG, while Ultra-CNet extracts depth-encoded features from cardiac data. The multimodal architecture merges features for final prediction using an MLP model. Single-modal techniques have limitations, highlighting the importance of multimodal classification in AF prediction. Fusion layer simplification in deep multimodal learning allows effective joint feature learning.

Comparative experiments demonstrate robustness and improved accuracy. However, standalone training accuracy is lower due to fewer ultrasound images. To address this, a stratified k-fold approach is employed for dataset splitting. Despite differences in standalone training, merging deep-encoded features maintains accuracy, emphasizing the effectiveness of the multimodal fusion model.

\begin{acknowledgement}
This study is financially supported by the National Science and Technology Council (NSTC) of Taiwan under Grants NSTC 112-2314-B-A49-049-MY3 and 112-2634-F-A49-003.
\end{acknowledgement}

\input{references302}

\end{document}
