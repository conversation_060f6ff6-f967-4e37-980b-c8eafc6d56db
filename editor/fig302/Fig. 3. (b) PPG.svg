<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="1440pt" height="1080pt" viewBox="0 0 1440 1080" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2024-04-01T17:26:00.274745</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.0, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 1080 
L 1440 1080 
L 1440 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 180 507.6 
L 1296 507.6 
L 1296 129.6 
L 180 129.6 
z
" style="fill: #ffffff"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <defs>
       <path id="m0e7b6fb55b" d="M 0 0 
L 0 3.5 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m0e7b6fb55b" x="180" y="507.6" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0 -->
      <g transform="translate(176.5 524.32125) scale(0.14 -0.14)">
       <defs>
        <path id="TimesNewRomanPSMT-30" d="M 231 2094 
Q 231 2819 450 3342 
Q 669 3866 1031 4122 
Q 1313 4325 1613 4325 
Q 2100 4325 2488 3828 
Q 2972 3213 2972 2159 
Q 2972 1422 2759 906 
Q 2547 391 2217 158 
Q 1888 -75 1581 -75 
Q 975 -75 572 641 
Q 231 1244 231 2094 
z
M 844 2016 
Q 844 1141 1059 588 
Q 1238 122 1591 122 
Q 1759 122 1940 273 
Q 2122 425 2216 781 
Q 2359 1319 2359 2297 
Q 2359 3022 2209 3506 
Q 2097 3866 1919 4016 
Q 1791 4119 1609 4119 
Q 1397 4119 1231 3928 
Q 1006 3669 925 3112 
Q 844 2556 844 2016 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#TimesNewRomanPSMT-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_2">
      <g>
       <use xlink:href="#m0e7b6fb55b" x="403.2" y="507.6" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 1 -->
      <g transform="translate(399.7 524.32125) scale(0.14 -0.14)">
       <defs>
        <path id="TimesNewRomanPSMT-31" d="M 750 3822 
L 1781 4325 
L 1884 4325 
L 1884 747 
Q 1884 391 1914 303 
Q 1944 216 2037 169 
Q 2131 122 2419 116 
L 2419 0 
L 825 0 
L 825 116 
Q 1125 122 1212 167 
Q 1300 213 1334 289 
Q 1369 366 1369 747 
L 1369 3034 
Q 1369 3497 1338 3628 
Q 1316 3728 1258 3775 
Q 1200 3822 1119 3822 
Q 1003 3822 797 3725 
L 750 3822 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#TimesNewRomanPSMT-31"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_3">
      <g>
       <use xlink:href="#m0e7b6fb55b" x="626.4" y="507.6" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 2 -->
      <g transform="translate(622.9 524.32125) scale(0.14 -0.14)">
       <defs>
        <path id="TimesNewRomanPSMT-32" d="M 2934 816 
L 2638 0 
L 138 0 
L 138 116 
Q 1241 1122 1691 1759 
Q 2141 2397 2141 2925 
Q 2141 3328 1894 3587 
Q 1647 3847 1303 3847 
Q 991 3847 742 3664 
Q 494 3481 375 3128 
L 259 3128 
Q 338 3706 661 4015 
Q 984 4325 1469 4325 
Q 1984 4325 2329 3994 
Q 2675 3663 2675 3213 
Q 2675 2891 2525 2569 
Q 2294 2063 1775 1497 
Q 997 647 803 472 
L 1909 472 
Q 2247 472 2383 497 
Q 2519 522 2628 598 
Q 2738 675 2819 816 
L 2934 816 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#TimesNewRomanPSMT-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_4">
      <g>
       <use xlink:href="#m0e7b6fb55b" x="849.6" y="507.6" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_4">
      <!-- 3 -->
      <g transform="translate(846.1 524.32125) scale(0.14 -0.14)">
       <defs>
        <path id="TimesNewRomanPSMT-33" d="M 325 3431 
Q 506 3859 782 4092 
Q 1059 4325 1472 4325 
Q 1981 4325 2253 3994 
Q 2459 3747 2459 3466 
Q 2459 3003 1878 2509 
Q 2269 2356 2469 2072 
Q 2669 1788 2669 1403 
Q 2669 853 2319 450 
Q 1863 -75 997 -75 
Q 569 -75 414 31 
Q 259 138 259 259 
Q 259 350 332 419 
Q 406 488 509 488 
Q 588 488 669 463 
Q 722 447 909 348 
Q 1097 250 1169 231 
Q 1284 197 1416 197 
Q 1734 197 1970 444 
Q 2206 691 2206 1028 
Q 2206 1275 2097 1509 
Q 2016 1684 1919 1775 
Q 1784 1900 1550 2001 
Q 1316 2103 1072 2103 
L 972 2103 
L 972 2197 
Q 1219 2228 1467 2375 
Q 1716 2522 1828 2728 
Q 1941 2934 1941 3181 
Q 1941 3503 1739 3701 
Q 1538 3900 1238 3900 
Q 753 3900 428 3381 
L 325 3431 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#TimesNewRomanPSMT-33"/>
      </g>
     </g>
    </g>
    <g id="xtick_5">
     <g id="line2d_5">
      <g>
       <use xlink:href="#m0e7b6fb55b" x="1072.8" y="507.6" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_5">
      <!-- 4 -->
      <g transform="translate(1069.3 524.32125) scale(0.14 -0.14)">
       <defs>
        <path id="TimesNewRomanPSMT-34" d="M 2978 1563 
L 2978 1119 
L 2409 1119 
L 2409 0 
L 1894 0 
L 1894 1119 
L 100 1119 
L 100 1519 
L 2066 4325 
L 2409 4325 
L 2409 1563 
L 2978 1563 
z
M 1894 1563 
L 1894 3666 
L 406 1563 
L 1894 1563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#TimesNewRomanPSMT-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_6">
     <g id="line2d_6">
      <g>
       <use xlink:href="#m0e7b6fb55b" x="1296" y="507.6" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_6">
      <!-- 5 -->
      <g transform="translate(1292.5 524.32125) scale(0.14 -0.14)">
       <defs>
        <path id="TimesNewRomanPSMT-35" d="M 2778 4238 
L 2534 3706 
L 1259 3706 
L 981 3138 
Q 1809 3016 2294 2522 
Q 2709 2097 2709 1522 
Q 2709 1188 2573 903 
Q 2438 619 2231 419 
Q 2025 219 1772 97 
Q 1413 -75 1034 -75 
Q 653 -75 479 54 
Q 306 184 306 341 
Q 306 428 378 495 
Q 450 563 559 563 
Q 641 563 702 538 
Q 763 513 909 409 
Q 1144 247 1384 247 
Q 1750 247 2026 523 
Q 2303 800 2303 1197 
Q 2303 1581 2056 1914 
Q 1809 2247 1375 2428 
Q 1034 2569 447 2591 
L 1259 4238 
L 2778 4238 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#TimesNewRomanPSMT-35"/>
      </g>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_7">
      <defs>
       <path id="m49d286f9c0" d="M 0 0 
L -3.5 0 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m49d286f9c0" x="180" y="467.027648" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_7">
      <!-- −0.15 -->
      <g transform="translate(140.605312 471.888273) scale(0.14 -0.14)">
       <defs>
        <path id="TimesNewRomanPSMT-2212" d="M 3484 2000 
L 116 2000 
L 116 2256 
L 3484 2256 
L 3484 2000 
z
" transform="scale(0.015625)"/>
        <path id="TimesNewRomanPSMT-2e" d="M 800 606 
Q 947 606 1047 504 
Q 1147 403 1147 259 
Q 1147 116 1045 14 
Q 944 -88 800 -88 
Q 656 -88 554 14 
Q 453 116 453 259 
Q 453 406 554 506 
Q 656 606 800 606 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#TimesNewRomanPSMT-2212"/>
       <use xlink:href="#TimesNewRomanPSMT-30" x="56.396484"/>
       <use xlink:href="#TimesNewRomanPSMT-2e" x="106.396484"/>
       <use xlink:href="#TimesNewRomanPSMT-31" x="131.396484"/>
       <use xlink:href="#TimesNewRomanPSMT-35" x="181.396484"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_8">
      <g>
       <use xlink:href="#m49d286f9c0" x="180" y="421.976919" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_8">
      <!-- −0.10 -->
      <g transform="translate(140.605312 426.837544) scale(0.14 -0.14)">
       <use xlink:href="#TimesNewRomanPSMT-2212"/>
       <use xlink:href="#TimesNewRomanPSMT-30" x="56.396484"/>
       <use xlink:href="#TimesNewRomanPSMT-2e" x="106.396484"/>
       <use xlink:href="#TimesNewRomanPSMT-31" x="131.396484"/>
       <use xlink:href="#TimesNewRomanPSMT-30" x="181.396484"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_9">
      <g>
       <use xlink:href="#m49d286f9c0" x="180" y="376.92619" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_9">
      <!-- −0.05 -->
      <g transform="translate(140.605312 381.786815) scale(0.14 -0.14)">
       <use xlink:href="#TimesNewRomanPSMT-2212"/>
       <use xlink:href="#TimesNewRomanPSMT-30" x="56.396484"/>
       <use xlink:href="#TimesNewRomanPSMT-2e" x="106.396484"/>
       <use xlink:href="#TimesNewRomanPSMT-30" x="131.396484"/>
       <use xlink:href="#TimesNewRomanPSMT-35" x="181.396484"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_10">
      <g>
       <use xlink:href="#m49d286f9c0" x="180" y="331.875461" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_10">
      <!-- 0.00 -->
      <g transform="translate(148.5 336.736086) scale(0.14 -0.14)">
       <use xlink:href="#TimesNewRomanPSMT-30"/>
       <use xlink:href="#TimesNewRomanPSMT-2e" x="50"/>
       <use xlink:href="#TimesNewRomanPSMT-30" x="75"/>
       <use xlink:href="#TimesNewRomanPSMT-30" x="125"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="line2d_11">
      <g>
       <use xlink:href="#m49d286f9c0" x="180" y="286.824731" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_11">
      <!-- 0.05 -->
      <g transform="translate(148.5 291.685356) scale(0.14 -0.14)">
       <use xlink:href="#TimesNewRomanPSMT-30"/>
       <use xlink:href="#TimesNewRomanPSMT-2e" x="50"/>
       <use xlink:href="#TimesNewRomanPSMT-30" x="75"/>
       <use xlink:href="#TimesNewRomanPSMT-35" x="125"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="line2d_12">
      <g>
       <use xlink:href="#m49d286f9c0" x="180" y="241.774002" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_12">
      <!-- 0.10 -->
      <g transform="translate(148.5 246.634627) scale(0.14 -0.14)">
       <use xlink:href="#TimesNewRomanPSMT-30"/>
       <use xlink:href="#TimesNewRomanPSMT-2e" x="50"/>
       <use xlink:href="#TimesNewRomanPSMT-31" x="75"/>
       <use xlink:href="#TimesNewRomanPSMT-30" x="125"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="line2d_13">
      <g>
       <use xlink:href="#m49d286f9c0" x="180" y="196.723273" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_13">
      <!-- 0.15 -->
      <g transform="translate(148.5 201.583898) scale(0.14 -0.14)">
       <use xlink:href="#TimesNewRomanPSMT-30"/>
       <use xlink:href="#TimesNewRomanPSMT-2e" x="50"/>
       <use xlink:href="#TimesNewRomanPSMT-31" x="75"/>
       <use xlink:href="#TimesNewRomanPSMT-35" x="125"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="line2d_14">
      <g>
       <use xlink:href="#m49d286f9c0" x="180" y="151.672543" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_14">
      <!-- 0.20 -->
      <g transform="translate(148.5 156.533168) scale(0.14 -0.14)">
       <use xlink:href="#TimesNewRomanPSMT-30"/>
       <use xlink:href="#TimesNewRomanPSMT-2e" x="50"/>
       <use xlink:href="#TimesNewRomanPSMT-32" x="75"/>
       <use xlink:href="#TimesNewRomanPSMT-30" x="125"/>
      </g>
     </g>
    </g>
   </g>
   <g id="line2d_15">
    <path d="M 180 364.385517 
L 183.5712 371.886891 
L 187.1424 379.792365 
L 190.7136 388.243501 
L 194.2848 397.48215 
L 197.856 407.777031 
L 201.4272 419.334676 
L 204.9984 432.202078 
L 208.5696 446.149297 
L 213.9264 467.510941 
L 215.712 474.088813 
L 217.4976 479.992932 
L 219.2832 484.916121 
L 221.0688 488.514426 
L 222.8544 490.418182 
L 224.64 490.248247 
L 226.4256 487.637433 
L 228.2112 482.256564 
L 229.9968 473.843889 
L 231.7824 462.235768 
L 233.568 447.395738 
L 235.3536 429.438373 
L 237.1392 408.644048 
L 238.9248 385.460966 
L 242.496 334.465346 
L 246.0672 282.509158 
L 247.8528 258.226829 
L 249.6384 236.06677 
L 251.424 216.617249 
L 253.2096 200.298639 
L 254.9952 187.345948 
L 256.7808 177.808024 
L 258.5664 171.561472 
L 260.352 168.336134 
L 262.1376 167.748647 
L 263.9232 169.340841 
L 265.7088 172.619944 
L 267.4944 177.097659 
L 269.28 182.325176 
L 276.4224 204.47041 
L 279.9936 214.497076 
L 283.5648 224.351027 
L 285.3504 229.572177 
L 287.136 235.170092 
L 288.9216 241.235086 
L 290.7072 247.802966 
L 294.2784 262.284543 
L 301.4208 292.681711 
L 303.2064 299.463277 
L 304.992 305.554407 
L 306.7776 310.83008 
L 308.5632 315.214735 
L 310.3488 318.68658 
L 312.1344 321.276995 
L 313.92 323.065305 
L 315.7056 324.169862 
L 317.4912 324.736782 
L 319.2768 324.927802 
L 324.6336 324.858694 
L 326.4192 325.09197 
L 328.2048 325.630448 
L 329.9904 326.536443 
L 331.776 327.840974 
L 333.5616 329.545205 
L 335.3472 331.623794 
L 337.1328 334.029731 
L 340.704 339.56169 
L 347.8464 351.388909 
L 349.632 354.09103 
L 351.4176 356.578831 
L 353.2032 358.814672 
L 354.9888 360.773071 
L 356.7744 362.440728 
L 358.56 363.81595 
L 360.3456 364.907642 
L 362.1312 365.734084 
L 363.9168 366.32154 
L 365.7024 366.702717 
L 367.488 366.915002 
L 371.0592 366.994449 
L 378.2016 366.871446 
L 381.7728 367.199831 
L 383.5584 367.547859 
L 385.344 368.039783 
L 387.1296 368.685497 
L 388.9152 369.489269 
L 390.7008 370.448878 
L 394.272 372.78977 
L 397.8432 375.541845 
L 404.9856 381.256259 
L 412.128 386.621307 
L 413.9136 388.228735 
L 415.6992 390.136036 
L 417.4848 392.447458 
L 419.2704 395.251423 
L 421.056 398.611075 
L 422.8416 402.558853 
L 424.6272 407.094352 
L 426.4128 412.183883 
L 428.1984 417.760274 
L 431.7696 429.931808 
L 435.3408 442.354376 
L 437.1264 448.096689 
L 438.912 453.146667 
L 440.6976 457.175195 
L 442.4832 459.827148 
L 444.2688 460.734638 
L 446.0544 459.535107 
L 447.84 455.893815 
L 449.6256 449.529716 
L 451.4112 440.243181 
L 453.1968 427.943379 
L 454.9824 412.672527 
L 456.768 394.623576 
L 458.5536 374.147692 
L 462.1248 328.060728 
L 467.4816 256.770471 
L 469.2672 235.461658 
L 471.0528 216.475118 
L 472.8384 200.271506 
L 474.624 187.140326 
L 476.4096 177.191644 
L 478.1952 170.363216 
L 479.9808 166.440823 
L 481.7664 165.088628 
L 483.552 165.885912 
L 485.3376 168.366663 
L 487.1232 172.058714 
L 488.9088 176.519508 
L 496.0512 195.674724 
L 499.6224 204.128464 
L 503.1936 212.37928 
L 504.9792 216.853478 
L 506.7648 221.803852 
L 508.5504 227.382017 
L 510.336 233.689885 
L 512.1216 240.765175 
L 513.9072 248.574252 
L 517.4784 265.915619 
L 522.8352 293.148133 
L 524.6208 301.581733 
L 526.4064 309.31693 
L 528.192 316.181932 
L 529.9776 322.058014 
L 531.7632 326.886127 
L 533.5488 330.668516 
L 535.3344 333.465178 
L 537.12 335.385703 
L 538.9056 336.577623 
L 540.6912 337.212818 
L 542.4768 337.473489 
L 547.8336 337.721821 
L 549.6192 338.09342 
L 551.4048 338.76848 
L 553.1904 339.792103 
L 554.976 341.176676 
L 556.7616 342.905298 
L 558.5472 344.936809 
L 562.1184 349.659446 
L 569.2608 359.658452 
L 571.0464 361.862091 
L 572.832 363.833192 
L 574.6176 365.531255 
L 576.4032 366.926976 
L 578.1888 368.003011 
L 579.9744 368.754271 
L 581.76 369.18761 
L 583.5456 369.320917 
L 585.3312 369.181677 
L 587.1168 368.805113 
L 588.9024 368.231991 
L 592.4736 366.672734 
L 599.616 363.096076 
L 603.1872 361.663012 
L 604.9728 361.138714 
L 606.7584 360.774183 
L 608.544 360.589525 
L 610.3296 360.601783 
L 612.1152 360.824616 
L 613.9008 361.267852 
L 615.6864 361.937024 
L 617.472 362.833084 
L 619.2576 363.952497 
L 621.0432 365.287858 
L 622.8288 366.829155 
L 624.6144 368.565666 
L 626.4 370.488387 
L 628.1856 372.592633 
L 629.9712 374.880315 
L 631.7568 377.361294 
L 633.5424 380.053461 
L 635.328 382.981488 
L 637.1136 386.174578 
L 638.8992 389.663667 
L 640.6848 393.478432 
L 642.4704 397.644219 
L 644.256 402.178748 
L 646.0416 407.088401 
L 649.6128 417.975736 
L 653.184 429.956508 
L 656.7552 442.192121 
L 658.5408 447.975587 
L 660.3264 453.222875 
L 662.112 457.648583 
L 663.8976 460.933693 
L 665.6832 462.735904 
L 667.4688 462.704365 
L 669.2544 460.498653 
L 671.04 455.811489 
L 672.8256 448.394145 
L 674.6112 438.083034 
L 676.3968 424.825422 
L 678.1824 408.701585 
L 679.968 389.940198 
L 681.7536 368.923488 
L 685.3248 322.357888 
L 688.896 274.467248 
L 690.6816 251.93005 
L 692.4672 231.272307 
L 694.2528 213.064988 
L 696.0384 197.726457 
L 697.824 185.501886 
L 699.6096 176.458691 
L 701.3952 170.497026 
L 703.1808 167.372831 
L 704.9664 166.729975 
L 706.752 168.137746 
L 708.5376 171.130126 
L 710.3232 175.243695 
L 712.1088 180.051469 
L 719.2512 200.269268 
L 722.8224 209.326143 
L 726.3936 218.313218 
L 728.1792 223.181656 
L 729.9648 228.514363 
L 731.7504 234.43328 
L 733.536 241.007884 
L 735.3216 248.244093 
L 738.8928 264.391876 
L 746.0352 298.385906 
L 747.8208 305.946209 
L 749.6064 312.73351 
L 751.392 318.617424 
L 753.1776 323.522367 
L 754.9632 327.430761 
L 756.7488 330.381434 
L 758.5344 332.46346 
L 760.32 333.806291 
L 762.1056 334.567516 
L 763.8912 334.919714 
L 769.248 335.214725 
L 771.0336 335.541392 
L 772.8192 336.156811 
L 774.6048 337.117226 
L 776.3904 338.445414 
L 778.176 340.133007 
L 779.9616 342.144833 
L 781.7472 344.424807 
L 785.3184 349.500917 
L 790.6752 357.245813 
L 792.4608 359.578896 
L 794.2464 361.690334 
L 796.032 363.534693 
L 797.8176 365.07624 
L 799.6032 366.289671 
L 801.3888 367.160606 
L 803.1744 367.685704 
L 804.96 367.872408 
L 806.7456 367.738374 
L 808.5312 367.310609 
L 810.3168 366.624233 
L 812.1024 365.720762 
L 815.6736 363.447261 
L 824.6016 357.118065 
L 828.1728 355.063705 
L 829.9584 354.235011 
L 831.744 353.560695 
L 833.5296 353.054454 
L 835.3152 352.727754 
L 837.1008 352.589686 
L 838.8864 352.646927 
L 840.672 352.904002 
L 842.4576 353.363846 
L 844.2432 354.028589 
L 846.0288 354.900479 
L 847.8144 355.982961 
L 849.6 357.281945 
L 851.3856 358.807229 
L 853.1712 360.573816 
L 854.9568 362.602757 
L 856.7424 364.92109 
L 858.528 367.560626 
L 860.3136 370.555516 
L 862.0992 373.93874 
L 863.8848 377.737874 
L 865.6704 381.970769 
L 867.456 386.642097 
L 869.2416 391.741783 
L 871.0272 397.245882 
L 874.5984 409.319761 
L 878.1696 422.499109 
L 885.312 449.83082 
L 887.0976 456.124884 
L 888.8832 461.803691 
L 890.6688 466.595472 
L 892.4544 470.191165 
L 894.24 472.252389 
L 896.0256 472.424004 
L 897.8112 470.351717 
L 899.5968 465.704722 
L 901.3824 458.202636 
L 903.168 447.645166 
L 904.9536 433.942021 
L 906.7392 417.139769 
L 908.5248 397.441801 
L 910.3104 375.217377 
L 913.8816 325.448162 
L 917.4528 273.50915 
L 919.2384 248.764666 
L 921.024 225.87364 
L 922.8096 205.491075 
L 924.5952 188.126177 
L 926.3808 174.117849 
L 928.1664 163.623768 
L 929.952 156.622264 
L 931.7376 152.925936 
L 933.5232 152.205812 
L 935.3088 154.024295 
L 937.0944 157.874239 
L 938.88 163.22066 
L 940.6656 169.541245 
L 946.0224 190.015102 
L 947.808 196.354016 
L 949.5936 202.217655 
L 951.3792 207.618455 
L 954.9504 217.464213 
L 958.5216 227.171785 
L 960.3072 232.412916 
L 962.0928 238.08568 
L 963.8784 244.252045 
L 967.4496 257.993308 
L 974.592 287.541471 
L 976.3776 294.261576 
L 978.1632 300.34846 
L 979.9488 305.679245 
L 981.7344 310.184613 
L 983.52 313.852097 
L 985.3056 316.723473 
L 987.0912 318.887375 
L 988.8768 320.468662 
L 990.6624 321.616167 
L 994.2336 323.25068 
L 996.0192 324.047102 
L 997.8048 325.009895 
L 999.5904 326.244317 
L 1001.376 327.826517 
L 1003.1616 329.801975 
L 1004.9472 332.186172 
L 1006.7328 334.967141 
L 1008.5184 338.109334 
L 1012.0896 345.244472 
L 1021.0176 364.357327 
L 1022.8032 367.755983 
L 1024.5888 370.866065 
L 1026.3744 373.651835 
L 1028.16 376.093089 
L 1029.9456 378.184388 
L 1031.7312 379.933659 
L 1033.5168 381.360332 
L 1035.3024 382.493064 
L 1037.088 383.367073 
L 1038.8736 384.021202 
L 1040.6592 384.494972 
L 1044.2304 385.048256 
L 1047.8016 385.279189 
L 1056.7296 385.581078 
L 1060.3008 385.948297 
L 1063.872 386.632665 
L 1065.6576 387.129346 
L 1067.4432 387.751674 
L 1069.2288 388.520966 
L 1071.0144 389.462776 
L 1072.8 390.606871 
L 1074.5856 391.986692 
L 1076.3712 393.638382 
L 1078.1568 395.599376 
L 1079.9424 397.906504 
L 1081.728 400.593534 
L 1083.5136 403.688222 
L 1085.2992 407.208998 
L 1087.0848 411.161507 
L 1088.8704 415.535171 
L 1090.656 420.299846 
L 1094.2272 430.764495 
L 1099.584 447.164151 
L 1101.3696 452.152669 
L 1103.1552 456.520393 
L 1104.9408 459.984345 
L 1106.7264 462.230437 
L 1108.512 462.921938 
L 1110.2976 461.712903 
L 1112.0832 458.266943 
L 1113.8688 452.280834 
L 1115.6544 443.511424 
L 1117.44 431.803362 
L 1119.2256 417.114738 
L 1121.0112 399.537807 
L 1122.7968 379.312414 
L 1124.5824 356.830137 
L 1128.1536 307.365509 
L 1131.7248 256.729107 
L 1133.5104 232.95363 
L 1135.296 211.209273 
L 1137.0816 192.121647 
L 1138.8672 176.16424 
L 1140.6528 163.632332 
L 1142.4384 154.633555 
L 1144.224 149.094608 
L 1146.0096 146.781818 
L 1147.7952 147.332132 
L 1149.5808 150.290789 
L 1151.3664 155.15205 
L 1153.152 161.399688 
L 1154.9376 168.544326 
L 1162.08 198.781828 
L 1165.6512 212.385096 
L 1174.5792 244.462844 
L 1178.1504 258.541743 
L 1185.2928 287.732345 
L 1187.0784 294.403032 
L 1188.864 300.502822 
L 1190.6496 305.909493 
L 1192.4352 310.541773 
L 1194.2208 314.364771 
L 1196.0064 317.390801 
L 1197.792 319.676147 
L 1199.5776 321.314518 
L 1201.3632 322.428072 
L 1203.1488 323.156969 
L 1206.72 324.046945 
L 1208.5056 324.484744 
L 1210.2912 325.075986 
L 1212.0768 325.91186 
L 1213.8624 327.058129 
L 1215.648 328.554142 
L 1217.4336 330.413117 
L 1219.2192 332.623495 
L 1221.0048 335.151367 
L 1224.576 340.933921 
L 1231.7184 353.308072 
L 1233.504 356.132589 
L 1235.2896 358.729182 
L 1237.0752 361.057814 
L 1238.8608 363.090533 
L 1240.6464 364.810879 
L 1242.432 366.21315 
L 1244.2176 367.301624 
L 1246.0032 368.089744 
L 1247.7888 368.599201 
L 1249.5744 368.858921 
L 1251.36 368.903941 
L 1253.1456 368.774145 
L 1256.7168 368.164762 
L 1262.0736 367.038245 
L 1265.6448 366.60471 
L 1267.4304 366.578266 
L 1269.216 366.711636 
L 1271.0016 367.02522 
L 1272.7872 367.536627 
L 1274.5728 368.261221 
L 1276.3584 369.212507 
L 1278.144 370.402193 
L 1279.9296 371.839864 
L 1281.7152 373.532359 
L 1283.5008 375.483138 
L 1285.2864 377.691896 
L 1287.072 380.154668 
L 1288.8576 382.864484 
L 1290.6432 385.812532 
L 1294.2144 392.388213 
L 1297.7856 399.833758 
L 1297.7856 399.833758 
" clip-path="url(#p502d34cb8f)" style="fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="patch_3">
    <path d="M 180 507.6 
L 180 129.6 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_4">
    <path d="M 1296 507.6 
L 1296 129.6 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_5">
    <path d="M 180 507.6 
L 1296 507.6 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_6">
    <path d="M 180 129.6 
L 1296 129.6 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_15">
    <!-- Raw PPG signal -->
    <g transform="translate(682.9275 123.6) scale(0.168 -0.168)">
     <defs>
      <path id="TimesNewRomanPSMT-52" d="M 4325 0 
L 3194 0 
L 1759 1981 
Q 1600 1975 1500 1975 
Q 1459 1975 1412 1976 
Q 1366 1978 1316 1981 
L 1316 750 
Q 1316 350 1403 253 
Q 1522 116 1759 116 
L 1925 116 
L 1925 0 
L 109 0 
L 109 116 
L 269 116 
Q 538 116 653 291 
Q 719 388 719 750 
L 719 3488 
Q 719 3888 631 3984 
Q 509 4122 269 4122 
L 109 4122 
L 109 4238 
L 1653 4238 
Q 2328 4238 2648 4139 
Q 2969 4041 3192 3777 
Q 3416 3513 3416 3147 
Q 3416 2756 3161 2468 
Q 2906 2181 2372 2063 
L 3247 847 
Q 3547 428 3762 290 
Q 3978 153 4325 116 
L 4325 0 
z
M 1316 2178 
Q 1375 2178 1419 2176 
Q 1463 2175 1491 2175 
Q 2097 2175 2405 2437 
Q 2713 2700 2713 3106 
Q 2713 3503 2464 3751 
Q 2216 4000 1806 4000 
Q 1625 4000 1316 3941 
L 1316 2178 
z
" transform="scale(0.015625)"/>
      <path id="TimesNewRomanPSMT-61" d="M 1822 413 
Q 1381 72 1269 19 
Q 1100 -59 909 -59 
Q 613 -59 420 144 
Q 228 347 228 678 
Q 228 888 322 1041 
Q 450 1253 767 1440 
Q 1084 1628 1822 1897 
L 1822 2009 
Q 1822 2438 1686 2597 
Q 1550 2756 1291 2756 
Q 1094 2756 978 2650 
Q 859 2544 859 2406 
L 866 2225 
Q 866 2081 792 2003 
Q 719 1925 600 1925 
Q 484 1925 411 2006 
Q 338 2088 338 2228 
Q 338 2497 613 2722 
Q 888 2947 1384 2947 
Q 1766 2947 2009 2819 
Q 2194 2722 2281 2516 
Q 2338 2381 2338 1966 
L 2338 994 
Q 2338 584 2353 492 
Q 2369 400 2405 369 
Q 2441 338 2488 338 
Q 2538 338 2575 359 
Q 2641 400 2828 588 
L 2828 413 
Q 2478 -56 2159 -56 
Q 2006 -56 1915 50 
Q 1825 156 1822 413 
z
M 1822 616 
L 1822 1706 
Q 1350 1519 1213 1441 
Q 966 1303 859 1153 
Q 753 1003 753 825 
Q 753 600 887 451 
Q 1022 303 1197 303 
Q 1434 303 1822 616 
z
" transform="scale(0.015625)"/>
      <path id="TimesNewRomanPSMT-77" d="M 41 2863 
L 1241 2863 
L 1241 2747 
Q 1075 2734 1023 2687 
Q 972 2641 972 2553 
Q 972 2456 1025 2319 
L 1638 672 
L 2253 2013 
L 2091 2434 
Q 2016 2622 1894 2694 
Q 1825 2738 1638 2747 
L 1638 2863 
L 3000 2863 
L 3000 2747 
Q 2775 2738 2681 2666 
Q 2619 2616 2619 2506 
Q 2619 2444 2644 2378 
L 3294 734 
L 3897 2319 
Q 3959 2488 3959 2588 
Q 3959 2647 3898 2694 
Q 3838 2741 3659 2747 
L 3659 2863 
L 4563 2863 
L 4563 2747 
Q 4291 2706 4163 2378 
L 3206 -88 
L 3078 -88 
L 2363 1741 
L 1528 -88 
L 1413 -88 
L 494 2319 
Q 403 2547 315 2626 
Q 228 2706 41 2747 
L 41 2863 
z
" transform="scale(0.015625)"/>
      <path id="TimesNewRomanPSMT-20" transform="scale(0.015625)"/>
      <path id="TimesNewRomanPSMT-50" d="M 1313 1984 
L 1313 750 
Q 1313 350 1400 253 
Q 1519 116 1759 116 
L 1922 116 
L 1922 0 
L 106 0 
L 106 116 
L 266 116 
Q 534 116 650 291 
Q 713 388 713 750 
L 713 3488 
Q 713 3888 628 3984 
Q 506 4122 266 4122 
L 106 4122 
L 106 4238 
L 1659 4238 
Q 2228 4238 2556 4120 
Q 2884 4003 3109 3725 
Q 3334 3447 3334 3066 
Q 3334 2547 2992 2222 
Q 2650 1897 2025 1897 
Q 1872 1897 1694 1919 
Q 1516 1941 1313 1984 
z
M 1313 2163 
Q 1478 2131 1606 2115 
Q 1734 2100 1825 2100 
Q 2150 2100 2386 2351 
Q 2622 2603 2622 3003 
Q 2622 3278 2509 3514 
Q 2397 3750 2190 3867 
Q 1984 3984 1722 3984 
Q 1563 3984 1313 3925 
L 1313 2163 
z
" transform="scale(0.015625)"/>
      <path id="TimesNewRomanPSMT-47" d="M 3928 4334 
L 4038 2997 
L 3928 2997 
Q 3763 3497 3500 3750 
Q 3122 4116 2528 4116 
Q 1719 4116 1297 3475 
Q 944 2934 944 2188 
Q 944 1581 1178 1081 
Q 1413 581 1792 348 
Q 2172 116 2572 116 
Q 2806 116 3025 175 
Q 3244 234 3447 350 
L 3447 1575 
Q 3447 1894 3398 1992 
Q 3350 2091 3248 2142 
Q 3147 2194 2891 2194 
L 2891 2313 
L 4531 2313 
L 4531 2194 
L 4453 2194 
Q 4209 2194 4119 2031 
Q 4056 1916 4056 1575 
L 4056 278 
Q 3697 84 3347 -6 
Q 2997 -97 2569 -97 
Q 1341 -97 703 691 
Q 225 1281 225 2053 
Q 225 2613 494 3125 
Q 813 3734 1369 4063 
Q 1834 4334 2469 4334 
Q 2700 4334 2889 4296 
Q 3078 4259 3425 4131 
Q 3600 4066 3659 4066 
Q 3719 4066 3761 4120 
Q 3803 4175 3813 4334 
L 3928 4334 
z
" transform="scale(0.015625)"/>
      <path id="TimesNewRomanPSMT-73" d="M 2050 2947 
L 2050 1972 
L 1947 1972 
Q 1828 2431 1642 2597 
Q 1456 2763 1169 2763 
Q 950 2763 815 2647 
Q 681 2531 681 2391 
Q 681 2216 781 2091 
Q 878 1963 1175 1819 
L 1631 1597 
Q 2266 1288 2266 781 
Q 2266 391 1970 151 
Q 1675 -88 1309 -88 
Q 1047 -88 709 6 
Q 606 38 541 38 
Q 469 38 428 -44 
L 325 -44 
L 325 978 
L 428 978 
Q 516 541 762 319 
Q 1009 97 1316 97 
Q 1531 97 1667 223 
Q 1803 350 1803 528 
Q 1803 744 1651 891 
Q 1500 1038 1047 1263 
Q 594 1488 453 1669 
Q 313 1847 313 2119 
Q 313 2472 555 2709 
Q 797 2947 1181 2947 
Q 1350 2947 1591 2875 
Q 1750 2828 1803 2828 
Q 1853 2828 1881 2850 
Q 1909 2872 1947 2947 
L 2050 2947 
z
" transform="scale(0.015625)"/>
      <path id="TimesNewRomanPSMT-69" d="M 928 4444 
Q 1059 4444 1151 4351 
Q 1244 4259 1244 4128 
Q 1244 3997 1151 3903 
Q 1059 3809 928 3809 
Q 797 3809 703 3903 
Q 609 3997 609 4128 
Q 609 4259 701 4351 
Q 794 4444 928 4444 
z
M 1188 2947 
L 1188 647 
Q 1188 378 1227 289 
Q 1266 200 1342 156 
Q 1419 113 1622 113 
L 1622 0 
L 231 0 
L 231 113 
Q 441 113 512 153 
Q 584 194 626 287 
Q 669 381 669 647 
L 669 1750 
Q 669 2216 641 2353 
Q 619 2453 572 2492 
Q 525 2531 444 2531 
Q 356 2531 231 2484 
L 188 2597 
L 1050 2947 
L 1188 2947 
z
" transform="scale(0.015625)"/>
      <path id="TimesNewRomanPSMT-67" d="M 966 1044 
Q 703 1172 562 1401 
Q 422 1631 422 1909 
Q 422 2334 742 2640 
Q 1063 2947 1563 2947 
Q 1972 2947 2272 2747 
L 2878 2747 
Q 3013 2747 3034 2739 
Q 3056 2731 3066 2713 
Q 3084 2684 3084 2613 
Q 3084 2531 3069 2500 
Q 3059 2484 3036 2475 
Q 3013 2466 2878 2466 
L 2506 2466 
Q 2681 2241 2681 1891 
Q 2681 1491 2375 1206 
Q 2069 922 1553 922 
Q 1341 922 1119 984 
Q 981 866 932 777 
Q 884 688 884 625 
Q 884 572 936 522 
Q 988 472 1138 450 
Q 1225 438 1575 428 
Q 2219 413 2409 384 
Q 2700 344 2873 169 
Q 3047 -6 3047 -263 
Q 3047 -616 2716 -925 
Q 2228 -1381 1444 -1381 
Q 841 -1381 425 -1109 
Q 191 -953 191 -784 
Q 191 -709 225 -634 
Q 278 -519 444 -313 
Q 466 -284 763 25 
Q 600 122 533 198 
Q 466 275 466 372 
Q 466 481 555 628 
Q 644 775 966 1044 
z
M 1509 2797 
Q 1278 2797 1122 2612 
Q 966 2428 966 2047 
Q 966 1553 1178 1281 
Q 1341 1075 1591 1075 
Q 1828 1075 1981 1253 
Q 2134 1431 2134 1813 
Q 2134 2309 1919 2591 
Q 1759 2797 1509 2797 
z
M 934 0 
Q 788 -159 713 -296 
Q 638 -434 638 -550 
Q 638 -700 819 -813 
Q 1131 -1006 1722 -1006 
Q 2284 -1006 2551 -807 
Q 2819 -609 2819 -384 
Q 2819 -222 2659 -153 
Q 2497 -84 2016 -72 
Q 1313 -53 934 0 
z
" transform="scale(0.015625)"/>
      <path id="TimesNewRomanPSMT-6e" d="M 1034 2341 
Q 1538 2947 1994 2947 
Q 2228 2947 2397 2830 
Q 2566 2713 2666 2444 
Q 2734 2256 2734 1869 
L 2734 647 
Q 2734 375 2778 278 
Q 2813 200 2889 156 
Q 2966 113 3172 113 
L 3172 0 
L 1756 0 
L 1756 113 
L 1816 113 
Q 2016 113 2095 173 
Q 2175 234 2206 353 
Q 2219 400 2219 647 
L 2219 1819 
Q 2219 2209 2117 2386 
Q 2016 2563 1775 2563 
Q 1403 2563 1034 2156 
L 1034 647 
Q 1034 356 1069 288 
Q 1113 197 1189 155 
Q 1266 113 1500 113 
L 1500 0 
L 84 0 
L 84 113 
L 147 113 
Q 366 113 442 223 
Q 519 334 519 647 
L 519 1709 
Q 519 2225 495 2337 
Q 472 2450 423 2490 
Q 375 2531 294 2531 
Q 206 2531 84 2484 
L 38 2597 
L 900 2947 
L 1034 2947 
L 1034 2341 
z
" transform="scale(0.015625)"/>
      <path id="TimesNewRomanPSMT-6c" d="M 1184 4444 
L 1184 647 
Q 1184 378 1223 290 
Q 1263 203 1344 158 
Q 1425 113 1647 113 
L 1647 0 
L 244 0 
L 244 113 
Q 441 113 512 153 
Q 584 194 625 287 
Q 666 381 666 647 
L 666 3247 
Q 666 3731 644 3842 
Q 622 3953 573 3993 
Q 525 4034 450 4034 
Q 369 4034 244 3984 
L 191 4094 
L 1044 4444 
L 1184 4444 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#TimesNewRomanPSMT-52"/>
     <use xlink:href="#TimesNewRomanPSMT-61" x="66.699219"/>
     <use xlink:href="#TimesNewRomanPSMT-77" x="111.083984"/>
     <use xlink:href="#TimesNewRomanPSMT-20" x="183.300781"/>
     <use xlink:href="#TimesNewRomanPSMT-50" x="208.300781"/>
     <use xlink:href="#TimesNewRomanPSMT-50" x="263.916016"/>
     <use xlink:href="#TimesNewRomanPSMT-47" x="319.53125"/>
     <use xlink:href="#TimesNewRomanPSMT-20" x="391.748047"/>
     <use xlink:href="#TimesNewRomanPSMT-73" x="416.748047"/>
     <use xlink:href="#TimesNewRomanPSMT-69" x="455.664062"/>
     <use xlink:href="#TimesNewRomanPSMT-67" x="483.447266"/>
     <use xlink:href="#TimesNewRomanPSMT-6e" x="533.447266"/>
     <use xlink:href="#TimesNewRomanPSMT-61" x="583.447266"/>
     <use xlink:href="#TimesNewRomanPSMT-6c" x="627.832031"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_7">
    <path d="M 180 961.2 
L 1296 961.2 
L 1296 583.2 
L 180 583.2 
z
" style="fill: #ffffff"/>
   </g>
   <g id="matplotlib.axis_3">
    <g id="xtick_7">
     <g id="line2d_16">
      <g>
       <use xlink:href="#m0e7b6fb55b" x="180" y="961.2" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_16">
      <!-- 0 -->
      <g transform="translate(176.5 977.92125) scale(0.14 -0.14)">
       <use xlink:href="#TimesNewRomanPSMT-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_8">
     <g id="line2d_17">
      <g>
       <use xlink:href="#m0e7b6fb55b" x="403.2" y="961.2" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_17">
      <!-- 1 -->
      <g transform="translate(399.7 977.92125) scale(0.14 -0.14)">
       <use xlink:href="#TimesNewRomanPSMT-31"/>
      </g>
     </g>
    </g>
    <g id="xtick_9">
     <g id="line2d_18">
      <g>
       <use xlink:href="#m0e7b6fb55b" x="626.4" y="961.2" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_18">
      <!-- 2 -->
      <g transform="translate(622.9 977.92125) scale(0.14 -0.14)">
       <use xlink:href="#TimesNewRomanPSMT-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_10">
     <g id="line2d_19">
      <g>
       <use xlink:href="#m0e7b6fb55b" x="849.6" y="961.2" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_19">
      <!-- 3 -->
      <g transform="translate(846.1 977.92125) scale(0.14 -0.14)">
       <use xlink:href="#TimesNewRomanPSMT-33"/>
      </g>
     </g>
    </g>
    <g id="xtick_11">
     <g id="line2d_20">
      <g>
       <use xlink:href="#m0e7b6fb55b" x="1072.8" y="961.2" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_20">
      <!-- 4 -->
      <g transform="translate(1069.3 977.92125) scale(0.14 -0.14)">
       <use xlink:href="#TimesNewRomanPSMT-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_12">
     <g id="line2d_21">
      <g>
       <use xlink:href="#m0e7b6fb55b" x="1296" y="961.2" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_21">
      <!-- 5 -->
      <g transform="translate(1292.5 977.92125) scale(0.14 -0.14)">
       <use xlink:href="#TimesNewRomanPSMT-35"/>
      </g>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_4">
    <g id="ytick_9">
     <g id="line2d_22">
      <g>
       <use xlink:href="#m49d286f9c0" x="180" y="933.337022" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_22">
      <!-- −0.2 -->
      <g transform="translate(147.605312 938.197647) scale(0.14 -0.14)">
       <use xlink:href="#TimesNewRomanPSMT-2212"/>
       <use xlink:href="#TimesNewRomanPSMT-30" x="56.396484"/>
       <use xlink:href="#TimesNewRomanPSMT-2e" x="106.396484"/>
       <use xlink:href="#TimesNewRomanPSMT-32" x="131.396484"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m49d286f9c0" x="180" y="853.264623" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_23">
      <!-- −0.1 -->
      <g transform="translate(147.605312 858.125248) scale(0.14 -0.14)">
       <use xlink:href="#TimesNewRomanPSMT-2212"/>
       <use xlink:href="#TimesNewRomanPSMT-30" x="56.396484"/>
       <use xlink:href="#TimesNewRomanPSMT-2e" x="106.396484"/>
       <use xlink:href="#TimesNewRomanPSMT-31" x="131.396484"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="line2d_24">
      <g>
       <use xlink:href="#m49d286f9c0" x="180" y="773.192224" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_24">
      <!-- 0.0 -->
      <g transform="translate(155.5 778.052849) scale(0.14 -0.14)">
       <use xlink:href="#TimesNewRomanPSMT-30"/>
       <use xlink:href="#TimesNewRomanPSMT-2e" x="50"/>
       <use xlink:href="#TimesNewRomanPSMT-30" x="75"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="line2d_25">
      <g>
       <use xlink:href="#m49d286f9c0" x="180" y="693.119825" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_25">
      <!-- 0.1 -->
      <g transform="translate(155.5 697.98045) scale(0.14 -0.14)">
       <use xlink:href="#TimesNewRomanPSMT-30"/>
       <use xlink:href="#TimesNewRomanPSMT-2e" x="50"/>
       <use xlink:href="#TimesNewRomanPSMT-31" x="75"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="line2d_26">
      <g>
       <use xlink:href="#m49d286f9c0" x="180" y="613.047426" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_26">
      <!-- 0.2 -->
      <g transform="translate(155.5 617.908051) scale(0.14 -0.14)">
       <use xlink:href="#TimesNewRomanPSMT-30"/>
       <use xlink:href="#TimesNewRomanPSMT-2e" x="50"/>
       <use xlink:href="#TimesNewRomanPSMT-32" x="75"/>
      </g>
     </g>
    </g>
   </g>
   <g id="line2d_27">
    <path d="M 180 834.64195 
L 185.3568 844.435017 
L 188.928 851.45528 
L 192.4992 859.075196 
L 196.0704 867.505018 
L 199.6416 876.976029 
L 203.2128 887.618174 
L 206.784 899.360903 
L 215.712 929.938069 
L 217.4976 935.076841 
L 219.2832 939.344026 
L 221.0688 942.433972 
L 222.8544 944.018182 
L 224.64 943.759708 
L 226.4256 941.332163 
L 228.2112 936.442883 
L 229.9968 928.859114 
L 231.7824 918.435371 
L 233.568 905.139328 
L 235.3536 889.073035 
L 237.1392 870.485965 
L 238.9248 849.776724 
L 242.496 804.246661 
L 246.0672 757.865112 
L 247.8528 736.181043 
L 249.6384 716.382293 
L 251.424 698.991667 
L 253.2096 684.383162 
L 254.9952 672.766242 
L 256.7808 664.184786 
L 258.5664 658.52919 
L 260.352 655.55902 
L 262.1376 654.93311 
L 263.9232 656.244014 
L 265.7088 659.053903 
L 267.4944 662.929185 
L 269.28 667.471331 
L 276.4224 686.741026 
L 279.9936 695.446489 
L 283.5648 704.000198 
L 285.3504 708.539141 
L 287.136 713.412977 
L 288.9216 718.701691 
L 290.7072 724.437039 
L 294.2784 737.103842 
L 301.4208 763.717228 
L 303.2064 769.644229 
L 304.992 774.957613 
L 306.7776 779.546492 
L 308.5632 783.343949 
L 310.3488 786.330721 
L 312.1344 788.534603 
L 313.92 790.025877 
L 315.7056 790.909516 
L 317.4912 791.315291 
L 319.2768 791.387012 
L 324.6336 791.033083 
L 326.4192 791.143629 
L 328.2048 791.525659 
L 329.9904 792.234441 
L 331.776 793.297479 
L 333.5616 794.715807 
L 335.3472 796.466968 
L 337.1328 798.509294 
L 340.704 803.235014 
L 347.8464 813.367873 
L 349.632 815.675015 
L 351.4176 817.791975 
L 353.2032 819.685392 
L 354.9888 821.332596 
L 356.7744 822.721654 
L 358.56 823.850956 
L 360.3456 824.728409 
L 362.1312 825.370337 
L 363.9168 825.800183 
L 365.7024 826.047047 
L 367.488 826.144107 
L 371.0592 826.032102 
L 378.2016 825.560174 
L 381.7728 825.672057 
L 383.5584 825.891737 
L 385.344 826.239627 
L 387.1296 826.724525 
L 388.9152 827.350126 
L 390.7008 828.114334 
L 394.272 830.017903 
L 397.8432 832.288738 
L 404.9856 837.019028 
L 412.128 841.44208 
L 413.9136 842.787051 
L 415.6992 844.398237 
L 417.4848 846.367655 
L 419.2704 848.773988 
L 421.056 851.674025 
L 422.8416 855.097334 
L 424.6272 859.043908 
L 426.4128 863.483672 
L 428.1984 868.356543 
L 431.7696 879.007927 
L 435.3408 889.883029 
L 437.1264 894.90427 
L 438.912 899.310503 
L 440.6976 902.809191 
L 442.4832 905.084764 
L 444.2688 905.810379 
L 446.0544 904.663892 
L 447.84 901.34773 
L 449.6256 895.611896 
L 451.4112 887.278735 
L 453.1968 876.267468 
L 454.9824 862.615836 
L 456.768 846.49574 
L 458.5536 828.219672 
L 462.1248 787.10691 
L 467.4816 723.52195 
L 469.2672 704.508054 
L 471.0528 687.557616 
L 472.8384 673.080166 
L 474.624 661.333257 
L 476.4096 652.415012 
L 478.1952 646.27032 
L 479.9808 642.708813 
L 481.7664 641.431848 
L 483.552 642.065291 
L 485.3376 644.194866 
L 487.1232 647.401045 
L 488.9088 651.290772 
L 496.0512 668.020566 
L 499.6224 675.387065 
L 503.1936 682.574248 
L 504.9792 686.478858 
L 506.7648 690.807274 
L 508.5504 695.693892 
L 510.336 701.228907 
L 512.1216 707.445635 
L 513.9072 714.314263 
L 517.4784 729.583673 
L 522.8352 753.577248 
L 524.6208 761.003426 
L 526.4064 767.808832 
L 528.192 773.840915 
L 529.9776 778.994456 
L 531.7632 783.21723 
L 533.5488 786.511282 
L 535.3344 788.929779 
L 537.12 790.569881 
L 538.9056 791.562544 
L 540.6912 792.060499 
L 542.4768 792.225754 
L 547.8336 792.249007 
L 549.6192 792.514142 
L 551.4048 793.049291 
L 553.1904 793.894473 
L 554.976 795.060617 
L 556.7616 796.53265 
L 558.5472 798.274016 
L 562.1184 802.343891 
L 569.2608 810.980513 
L 571.0464 812.876913 
L 572.832 814.566789 
L 574.6176 816.014293 
L 576.4032 817.193489 
L 578.1888 818.088967 
L 579.9744 818.696127 
L 581.76 819.020982 
L 583.5456 819.079417 
L 585.3312 818.895913 
L 587.1168 818.501834 
L 588.9024 817.933408 
L 592.4736 816.430231 
L 599.616 813.019346 
L 603.1872 811.631512 
L 604.9728 811.108787 
L 606.7584 810.728257 
L 608.544 810.507824 
L 610.3296 810.462673 
L 612.1152 810.604959 
L 613.9008 810.943414 
L 615.6864 811.482958 
L 617.472 812.224437 
L 619.2576 813.164678 
L 621.0432 814.297031 
L 622.8288 815.612521 
L 624.6144 817.101607 
L 626.4 818.756376 
L 628.1856 820.572827 
L 629.9712 822.55278 
L 631.7568 824.704991 
L 633.5424 827.045216 
L 635.328 829.595195 
L 637.1136 832.380774 
L 638.8992 835.429479 
L 640.6848 838.767818 
L 642.4704 842.418446 
L 644.256 846.39716 
L 646.0416 850.709581 
L 649.6128 860.284275 
L 653.184 870.831584 
L 656.7552 881.606587 
L 658.5408 886.697409 
L 660.3264 891.311963 
L 662.112 895.196575 
L 663.8976 898.067747 
L 665.6832 899.621351 
L 667.4688 899.545679 
L 669.2544 897.538234 
L 671.04 893.325833 
L 672.8256 886.687192 
L 674.6112 877.476681 
L 676.3968 865.647351 
L 678.1824 851.270746 
L 679.968 834.550523 
L 681.7536 815.826837 
L 685.3248 774.35492 
L 688.896 731.707892 
L 690.6816 711.6356 
L 692.4672 693.233133 
L 694.2528 677.007948 
L 696.0384 663.332061 
L 697.824 652.42366 
L 699.6096 644.342936 
L 701.3952 639.00133 
L 703.1808 636.181944 
L 704.9664 635.568125 
L 706.752 636.77688 
L 708.5376 639.393914 
L 710.3232 643.007416 
L 712.1088 647.238102 
L 719.2512 665.041782 
L 722.8224 673.009339 
L 726.3936 680.915667 
L 728.1792 685.202776 
L 729.9648 689.902948 
L 731.7504 695.12431 
L 733.536 700.928337 
L 735.3216 707.320131 
L 738.8928 721.592362 
L 746.0352 751.651229 
L 747.8208 758.332925 
L 749.6064 764.32758 
L 751.392 769.519428 
L 753.1776 773.841551 
L 754.9632 777.278527 
L 756.7488 779.864865 
L 758.5344 781.679541 
L 760.32 782.837375 
L 762.1056 783.478301 
L 763.8912 783.755728 
L 769.248 783.912495 
L 771.0336 784.168306 
L 772.8192 784.681011 
L 774.6048 785.500518 
L 776.3904 786.646973 
L 778.176 788.112852 
L 779.9616 789.866887 
L 781.7472 791.85934 
L 785.3184 796.30405 
L 790.6752 803.090093 
L 792.4608 805.131269 
L 794.2464 806.975423 
L 796.032 808.582371 
L 797.8176 809.920538 
L 799.6032 810.967488 
L 801.3888 811.710338 
L 803.1744 812.145972 
L 804.96 812.280925 
L 806.7456 812.130932 
L 808.5312 811.720109 
L 810.3168 811.079751 
L 812.1024 810.246748 
L 815.6736 808.166655 
L 824.6016 802.395064 
L 828.1728 800.511716 
L 829.9584 799.746811 
L 831.744 799.119363 
L 833.5296 798.64146 
L 835.3152 798.323188 
L 837.1008 798.17259 
L 838.8864 798.195657 
L 840.672 798.396518 
L 842.4576 798.777845 
L 844.2432 799.341503 
L 846.0288 800.0894 
L 847.8144 801.024525 
L 849.6 802.152125 
L 851.3856 803.480937 
L 853.1712 805.024308 
L 854.9568 806.800923 
L 856.7424 808.834824 
L 858.528 811.154404 
L 860.3136 813.79022 
L 862.0992 816.771701 
L 863.8848 820.123192 
L 865.6704 823.860117 
L 867.456 827.986162 
L 869.2416 832.492248 
L 871.0272 837.357465 
L 874.5984 848.036692 
L 878.1696 859.701682 
L 885.312 883.894422 
L 887.0976 889.463651 
L 888.8832 894.486628 
L 890.6688 898.721765 
L 892.4544 901.894195 
L 894.24 903.703022 
L 896.0256 903.83258 
L 897.8112 901.968042 
L 899.5968 897.815425 
L 901.3824 891.125444 
L 903.168 881.7199 
L 904.9536 869.518414 
L 906.7392 854.562494 
L 908.5248 837.033337 
L 910.3104 817.259736 
L 913.8816 772.98812 
L 919.2384 704.778601 
L 921.024 684.412997 
L 922.8096 666.276145 
L 924.5952 650.821484 
L 926.3808 638.350756 
L 928.1664 629.00398 
L 929.952 622.761284 
L 931.7376 619.455498 
L 933.5232 618.794053 
L 935.3088 620.38834 
L 937.0944 623.788174 
L 938.88 628.518476 
L 940.6656 634.11506 
L 946.0224 652.248852 
L 947.808 657.861493 
L 949.5936 663.051951 
L 951.3792 667.831517 
L 954.9504 676.54228 
L 958.5216 685.129806 
L 960.3072 689.767276 
L 962.0928 694.788263 
L 963.8784 700.248169 
L 967.4496 712.420926 
L 974.592 738.601271 
L 976.3776 744.553471 
L 978.1632 749.943356 
L 979.9488 754.661782 
L 981.7344 758.646998 
L 983.52 761.887672 
L 985.3056 764.420708 
L 987.0912 766.324777 
L 988.8768 767.710882 
L 990.6624 768.711419 
L 994.2336 770.125848 
L 996.0192 770.814831 
L 997.8048 771.651792 
L 999.5904 772.730144 
L 1001.376 774.117443 
L 1003.1616 775.854073 
L 1004.9472 777.953885 
L 1006.7328 780.406421 
L 1008.5184 783.180242 
L 1012.0896 789.484607 
L 1021.0176 806.376758 
L 1022.8032 809.378743 
L 1024.5888 812.124454 
L 1026.3744 814.581936 
L 1028.16 816.733078 
L 1029.9456 818.573029 
L 1031.7312 820.108945 
L 1033.5168 821.35824 
L 1035.3024 822.346479 
L 1037.088 823.104956 
L 1038.8736 823.668102 
L 1040.6592 824.070922 
L 1044.2304 824.525737 
L 1047.8016 824.693624 
L 1056.7296 824.870231 
L 1060.3008 825.159564 
L 1063.872 825.73026 
L 1067.4432 826.687584 
L 1069.2288 827.352815 
L 1071.0144 828.171301 
L 1072.8 829.169412 
L 1074.5856 830.376877 
L 1076.3712 831.825899 
L 1078.1568 833.549835 
L 1079.9424 835.581442 
L 1081.728 837.950685 
L 1083.5136 840.682156 
L 1085.2992 843.792184 
L 1087.0848 847.285793 
L 1088.8704 851.153621 
L 1090.656 855.368907 
L 1094.2272 864.630519 
L 1099.584 879.146915 
L 1101.3696 883.561047 
L 1103.1552 887.423692 
L 1104.9408 890.483276 
L 1106.7264 892.46043 
L 1108.512 893.055632 
L 1110.2976 891.961331 
L 1112.0832 888.878749 
L 1113.8688 883.53879 
L 1115.6544 875.725691 
L 1117.44 865.301383 
L 1119.2256 852.228227 
L 1121.0112 836.587699 
L 1122.7968 818.592741 
L 1124.5824 798.591675 
L 1128.1536 754.591843 
L 1131.7248 709.554252 
L 1133.5104 688.406531 
L 1135.296 669.062955 
L 1137.0816 652.079261 
L 1138.8672 637.876477 
L 1140.6528 626.717541 
L 1142.4384 618.69862 
L 1144.224 613.754862 
L 1146.0096 611.678599 
L 1147.7952 612.146957 
L 1149.5808 614.755487 
L 1151.3664 619.054519 
L 1153.152 624.585231 
L 1154.9376 630.912841 
L 1162.08 657.698581 
L 1165.6512 669.74464 
L 1174.5792 698.140868 
L 1178.1504 710.60769 
L 1185.2928 736.455482 
L 1187.0784 742.36043 
L 1188.864 747.758207 
L 1190.6496 752.540004 
L 1192.4352 756.63339 
L 1194.2208 760.00726 
L 1196.0064 762.672608 
L 1197.792 764.679593 
L 1199.5776 766.111614 
L 1201.3632 767.077242 
L 1203.1488 767.700932 
L 1206.72 768.4429 
L 1208.5056 768.807053 
L 1210.2912 769.307415 
L 1212.0768 770.025162 
L 1213.8624 771.018799 
L 1215.648 772.323267 
L 1217.4336 773.950218 
L 1219.2192 775.889288 
L 1221.0048 778.110277 
L 1224.576 783.197384 
L 1231.7184 794.089054 
L 1233.504 796.572863 
L 1235.2896 798.853954 
L 1237.0752 800.89664 
L 1238.8608 802.676063 
L 1240.6464 804.177673 
L 1242.432 805.396497 
L 1244.2176 806.336374 
L 1246.0032 807.009211 
L 1247.7888 807.434226 
L 1249.5744 807.63711 
L 1251.36 807.649049 
L 1253.1456 807.505545 
L 1256.7168 806.907401 
L 1262.0736 805.819817 
L 1265.6448 805.376115 
L 1267.4304 805.323247 
L 1269.216 805.412211 
L 1271.0016 805.661092 
L 1272.7872 806.085563 
L 1274.5728 806.699355 
L 1276.3584 807.514532 
L 1278.144 808.541494 
L 1279.9296 809.78869 
L 1281.7152 811.262137 
L 1283.5008 812.964898 
L 1285.2864 814.896744 
L 1287.072 817.054188 
L 1288.8576 819.431015 
L 1290.6432 822.019315 
L 1294.2144 827.798998 
L 1297.7856 834.351265 
L 1297.7856 834.351265 
" clip-path="url(#p60cc705fab)" style="fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="patch_8">
    <path d="M 180 961.2 
L 180 583.2 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_9">
    <path d="M 1296 961.2 
L 1296 583.2 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_10">
    <path d="M 180 961.2 
L 1296 961.2 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_11">
    <path d="M 180 583.2 
L 1296 583.2 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_27">
    <!-- Clean PPG signal -->
    <g transform="translate(678.731437 577.2) scale(0.168 -0.168)">
     <defs>
      <path id="TimesNewRomanPSMT-43" d="M 3853 4334 
L 3950 2894 
L 3853 2894 
Q 3659 3541 3300 3825 
Q 2941 4109 2438 4109 
Q 2016 4109 1675 3895 
Q 1334 3681 1139 3212 
Q 944 2744 944 2047 
Q 944 1472 1128 1050 
Q 1313 628 1683 403 
Q 2053 178 2528 178 
Q 2941 178 3256 354 
Q 3572 531 3950 1056 
L 4047 994 
Q 3728 428 3303 165 
Q 2878 -97 2294 -97 
Q 1241 -97 663 684 
Q 231 1266 231 2053 
Q 231 2688 515 3219 
Q 800 3750 1298 4042 
Q 1797 4334 2388 4334 
Q 2847 4334 3294 4109 
Q 3425 4041 3481 4041 
Q 3566 4041 3628 4100 
Q 3709 4184 3744 4334 
L 3853 4334 
z
" transform="scale(0.015625)"/>
      <path id="TimesNewRomanPSMT-65" d="M 681 1784 
Q 678 1147 991 784 
Q 1303 422 1725 422 
Q 2006 422 2214 576 
Q 2422 731 2563 1106 
L 2659 1044 
Q 2594 616 2278 264 
Q 1963 -88 1488 -88 
Q 972 -88 605 314 
Q 238 716 238 1394 
Q 238 2128 614 2539 
Q 991 2950 1559 2950 
Q 2041 2950 2350 2633 
Q 2659 2316 2659 1784 
L 681 1784 
z
M 681 1966 
L 2006 1966 
Q 1991 2241 1941 2353 
Q 1863 2528 1708 2628 
Q 1553 2728 1384 2728 
Q 1125 2728 920 2526 
Q 716 2325 681 1966 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#TimesNewRomanPSMT-43"/>
     <use xlink:href="#TimesNewRomanPSMT-6c" x="66.699219"/>
     <use xlink:href="#TimesNewRomanPSMT-65" x="94.482422"/>
     <use xlink:href="#TimesNewRomanPSMT-61" x="138.867188"/>
     <use xlink:href="#TimesNewRomanPSMT-6e" x="183.251953"/>
     <use xlink:href="#TimesNewRomanPSMT-20" x="233.251953"/>
     <use xlink:href="#TimesNewRomanPSMT-50" x="258.251953"/>
     <use xlink:href="#TimesNewRomanPSMT-50" x="313.867188"/>
     <use xlink:href="#TimesNewRomanPSMT-47" x="369.482422"/>
     <use xlink:href="#TimesNewRomanPSMT-20" x="441.699219"/>
     <use xlink:href="#TimesNewRomanPSMT-73" x="466.699219"/>
     <use xlink:href="#TimesNewRomanPSMT-69" x="505.615234"/>
     <use xlink:href="#TimesNewRomanPSMT-67" x="533.398438"/>
     <use xlink:href="#TimesNewRomanPSMT-6e" x="583.398438"/>
     <use xlink:href="#TimesNewRomanPSMT-61" x="633.398438"/>
     <use xlink:href="#TimesNewRomanPSMT-6c" x="677.783203"/>
    </g>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="p502d34cb8f">
   <rect x="180" y="129.6" width="1116" height="378"/>
  </clipPath>
  <clipPath id="p60cc705fab">
   <rect x="180" y="583.2" width="1116" height="378"/>
  </clipPath>
 </defs>
</svg>
