<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="460.8pt" height="345.6pt" viewBox="0 0 460.8 345.6" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2024-04-07T10:29:09.595990</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.0, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 345.6 
L 460.8 345.6 
L 460.8 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 57.6 307.584 
L 414.72 307.584 
L 414.72 41.472 
L 57.6 41.472 
z
" style="fill: #ffffff"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <defs>
       <path id="m3d4f13c6de" d="M 0 0 
L 0 3.5 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m3d4f13c6de" x="73.832727" y="307.584" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 0 -->
      <g transform="translate(70.332727 324.30525) scale(0.14 -0.14)">
       <defs>
        <path id="TimesNewRomanPSMT-30" d="M 231 2094 
Q 231 2819 450 3342 
Q 669 3866 1031 4122 
Q 1313 4325 1613 4325 
Q 2100 4325 2488 3828 
Q 2972 3213 2972 2159 
Q 2972 1422 2759 906 
Q 2547 391 2217 158 
Q 1888 -75 1581 -75 
Q 975 -75 572 641 
Q 231 1244 231 2094 
z
M 844 2016 
Q 844 1141 1059 588 
Q 1238 122 1591 122 
Q 1759 122 1940 273 
Q 2122 425 2216 781 
Q 2359 1319 2359 2297 
Q 2359 3022 2209 3506 
Q 2097 3866 1919 4016 
Q 1791 4119 1609 4119 
Q 1397 4119 1231 3928 
Q 1006 3669 925 3112 
Q 844 2556 844 2016 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#TimesNewRomanPSMT-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_2">
      <g>
       <use xlink:href="#m3d4f13c6de" x="138.893758" y="307.584" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 100 -->
      <g transform="translate(128.393758 324.30525) scale(0.14 -0.14)">
       <defs>
        <path id="TimesNewRomanPSMT-31" d="M 750 3822 
L 1781 4325 
L 1884 4325 
L 1884 747 
Q 1884 391 1914 303 
Q 1944 216 2037 169 
Q 2131 122 2419 116 
L 2419 0 
L 825 0 
L 825 116 
Q 1125 122 1212 167 
Q 1300 213 1334 289 
Q 1369 366 1369 747 
L 1369 3034 
Q 1369 3497 1338 3628 
Q 1316 3728 1258 3775 
Q 1200 3822 1119 3822 
Q 1003 3822 797 3725 
L 750 3822 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#TimesNewRomanPSMT-31"/>
       <use xlink:href="#TimesNewRomanPSMT-30" x="50"/>
       <use xlink:href="#TimesNewRomanPSMT-30" x="100"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_3">
      <g>
       <use xlink:href="#m3d4f13c6de" x="203.95479" y="307.584" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 200 -->
      <g transform="translate(193.45479 324.30525) scale(0.14 -0.14)">
       <defs>
        <path id="TimesNewRomanPSMT-32" d="M 2934 816 
L 2638 0 
L 138 0 
L 138 116 
Q 1241 1122 1691 1759 
Q 2141 2397 2141 2925 
Q 2141 3328 1894 3587 
Q 1647 3847 1303 3847 
Q 991 3847 742 3664 
Q 494 3481 375 3128 
L 259 3128 
Q 338 3706 661 4015 
Q 984 4325 1469 4325 
Q 1984 4325 2329 3994 
Q 2675 3663 2675 3213 
Q 2675 2891 2525 2569 
Q 2294 2063 1775 1497 
Q 997 647 803 472 
L 1909 472 
Q 2247 472 2383 497 
Q 2519 522 2628 598 
Q 2738 675 2819 816 
L 2934 816 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#TimesNewRomanPSMT-32"/>
       <use xlink:href="#TimesNewRomanPSMT-30" x="50"/>
       <use xlink:href="#TimesNewRomanPSMT-30" x="100"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_4">
      <g>
       <use xlink:href="#m3d4f13c6de" x="269.015821" y="307.584" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_4">
      <!-- 300 -->
      <g transform="translate(258.515821 324.30525) scale(0.14 -0.14)">
       <defs>
        <path id="TimesNewRomanPSMT-33" d="M 325 3431 
Q 506 3859 782 4092 
Q 1059 4325 1472 4325 
Q 1981 4325 2253 3994 
Q 2459 3747 2459 3466 
Q 2459 3003 1878 2509 
Q 2269 2356 2469 2072 
Q 2669 1788 2669 1403 
Q 2669 853 2319 450 
Q 1863 -75 997 -75 
Q 569 -75 414 31 
Q 259 138 259 259 
Q 259 350 332 419 
Q 406 488 509 488 
Q 588 488 669 463 
Q 722 447 909 348 
Q 1097 250 1169 231 
Q 1284 197 1416 197 
Q 1734 197 1970 444 
Q 2206 691 2206 1028 
Q 2206 1275 2097 1509 
Q 2016 1684 1919 1775 
Q 1784 1900 1550 2001 
Q 1316 2103 1072 2103 
L 972 2103 
L 972 2197 
Q 1219 2228 1467 2375 
Q 1716 2522 1828 2728 
Q 1941 2934 1941 3181 
Q 1941 3503 1739 3701 
Q 1538 3900 1238 3900 
Q 753 3900 428 3381 
L 325 3431 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#TimesNewRomanPSMT-33"/>
       <use xlink:href="#TimesNewRomanPSMT-30" x="50"/>
       <use xlink:href="#TimesNewRomanPSMT-30" x="100"/>
      </g>
     </g>
    </g>
    <g id="xtick_5">
     <g id="line2d_5">
      <g>
       <use xlink:href="#m3d4f13c6de" x="334.076852" y="307.584" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_5">
      <!-- 400 -->
      <g transform="translate(323.576852 324.30525) scale(0.14 -0.14)">
       <defs>
        <path id="TimesNewRomanPSMT-34" d="M 2978 1563 
L 2978 1119 
L 2409 1119 
L 2409 0 
L 1894 0 
L 1894 1119 
L 100 1119 
L 100 1519 
L 2066 4325 
L 2409 4325 
L 2409 1563 
L 2978 1563 
z
M 1894 1563 
L 1894 3666 
L 406 1563 
L 1894 1563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#TimesNewRomanPSMT-34"/>
       <use xlink:href="#TimesNewRomanPSMT-30" x="50"/>
       <use xlink:href="#TimesNewRomanPSMT-30" x="100"/>
      </g>
     </g>
    </g>
    <g id="xtick_6">
     <g id="line2d_6">
      <g>
       <use xlink:href="#m3d4f13c6de" x="399.137883" y="307.584" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_6">
      <!-- 500 -->
      <g transform="translate(388.637883 324.30525) scale(0.14 -0.14)">
       <defs>
        <path id="TimesNewRomanPSMT-35" d="M 2778 4238 
L 2534 3706 
L 1259 3706 
L 981 3138 
Q 1809 3016 2294 2522 
Q 2709 2097 2709 1522 
Q 2709 1188 2573 903 
Q 2438 619 2231 419 
Q 2025 219 1772 97 
Q 1413 -75 1034 -75 
Q 653 -75 479 54 
Q 306 184 306 341 
Q 306 428 378 495 
Q 450 563 559 563 
Q 641 563 702 538 
Q 763 513 909 409 
Q 1144 247 1384 247 
Q 1750 247 2026 523 
Q 2303 800 2303 1197 
Q 2303 1581 2056 1914 
Q 1809 2247 1375 2428 
Q 1034 2569 447 2591 
L 1259 4238 
L 2778 4238 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#TimesNewRomanPSMT-35"/>
       <use xlink:href="#TimesNewRomanPSMT-30" x="50"/>
       <use xlink:href="#TimesNewRomanPSMT-30" x="100"/>
      </g>
     </g>
    </g>
    <g id="text_7">
     <!-- Epoch -->
     <g transform="translate(218.277187 341.021187) scale(0.14 -0.14)">
      <defs>
       <path id="TimesNewRomanPSMT-45" d="M 1338 4006 
L 1338 2331 
L 2269 2331 
Q 2631 2331 2753 2441 
Q 2916 2584 2934 2947 
L 3050 2947 
L 3050 1472 
L 2934 1472 
Q 2891 1781 2847 1869 
Q 2791 1978 2662 2040 
Q 2534 2103 2269 2103 
L 1338 2103 
L 1338 706 
Q 1338 425 1363 364 
Q 1388 303 1450 267 
Q 1513 231 1688 231 
L 2406 231 
Q 2766 231 2928 281 
Q 3091 331 3241 478 
Q 3434 672 3638 1063 
L 3763 1063 
L 3397 0 
L 131 0 
L 131 116 
L 281 116 
Q 431 116 566 188 
Q 666 238 702 338 
Q 738 438 738 747 
L 738 3500 
Q 738 3903 656 3997 
Q 544 4122 281 4122 
L 131 4122 
L 131 4238 
L 3397 4238 
L 3444 3309 
L 3322 3309 
Q 3256 3644 3176 3769 
Q 3097 3894 2941 3959 
Q 2816 4006 2500 4006 
L 1338 4006 
z
" transform="scale(0.015625)"/>
       <path id="TimesNewRomanPSMT-70" d="M -6 2578 
L 875 2934 
L 994 2934 
L 994 2266 
Q 1216 2644 1439 2795 
Q 1663 2947 1909 2947 
Q 2341 2947 2628 2609 
Q 2981 2197 2981 1534 
Q 2981 794 2556 309 
Q 2206 -88 1675 -88 
Q 1444 -88 1275 -22 
Q 1150 25 994 166 
L 994 -706 
Q 994 -1000 1030 -1079 
Q 1066 -1159 1155 -1206 
Q 1244 -1253 1478 -1253 
L 1478 -1369 
L -22 -1369 
L -22 -1253 
L 56 -1253 
Q 228 -1256 350 -1188 
Q 409 -1153 442 -1076 
Q 475 -1000 475 -688 
L 475 2019 
Q 475 2297 450 2372 
Q 425 2447 370 2484 
Q 316 2522 222 2522 
Q 147 2522 31 2478 
L -6 2578 
z
M 994 2081 
L 994 1013 
Q 994 666 1022 556 
Q 1066 375 1236 237 
Q 1406 100 1666 100 
Q 1978 100 2172 344 
Q 2425 663 2425 1241 
Q 2425 1897 2138 2250 
Q 1938 2494 1663 2494 
Q 1513 2494 1366 2419 
Q 1253 2363 994 2081 
z
" transform="scale(0.015625)"/>
       <path id="TimesNewRomanPSMT-6f" d="M 1600 2947 
Q 2250 2947 2644 2453 
Q 2978 2031 2978 1484 
Q 2978 1100 2793 706 
Q 2609 313 2286 112 
Q 1963 -88 1566 -88 
Q 919 -88 538 428 
Q 216 863 216 1403 
Q 216 1797 411 2186 
Q 606 2575 925 2761 
Q 1244 2947 1600 2947 
z
M 1503 2744 
Q 1338 2744 1170 2645 
Q 1003 2547 900 2300 
Q 797 2053 797 1666 
Q 797 1041 1045 587 
Q 1294 134 1700 134 
Q 2003 134 2200 384 
Q 2397 634 2397 1244 
Q 2397 2006 2069 2444 
Q 1847 2744 1503 2744 
z
" transform="scale(0.015625)"/>
       <path id="TimesNewRomanPSMT-63" d="M 2631 1088 
Q 2516 522 2178 217 
Q 1841 -88 1431 -88 
Q 944 -88 581 321 
Q 219 731 219 1428 
Q 219 2103 620 2525 
Q 1022 2947 1584 2947 
Q 2006 2947 2278 2723 
Q 2550 2500 2550 2259 
Q 2550 2141 2473 2067 
Q 2397 1994 2259 1994 
Q 2075 1994 1981 2113 
Q 1928 2178 1911 2362 
Q 1894 2547 1784 2644 
Q 1675 2738 1481 2738 
Q 1169 2738 978 2506 
Q 725 2200 725 1697 
Q 725 1184 976 792 
Q 1228 400 1656 400 
Q 1963 400 2206 609 
Q 2378 753 2541 1131 
L 2631 1088 
z
" transform="scale(0.015625)"/>
       <path id="TimesNewRomanPSMT-68" d="M 1041 4444 
L 1041 2350 
Q 1388 2731 1591 2839 
Q 1794 2947 1997 2947 
Q 2241 2947 2416 2812 
Q 2591 2678 2675 2391 
Q 2734 2191 2734 1659 
L 2734 647 
Q 2734 375 2778 275 
Q 2809 200 2884 156 
Q 2959 113 3159 113 
L 3159 0 
L 1753 0 
L 1753 113 
L 1819 113 
Q 2019 113 2097 173 
Q 2175 234 2206 353 
Q 2216 403 2216 647 
L 2216 1659 
Q 2216 2128 2167 2275 
Q 2119 2422 2012 2495 
Q 1906 2569 1756 2569 
Q 1603 2569 1437 2487 
Q 1272 2406 1041 2159 
L 1041 647 
Q 1041 353 1073 281 
Q 1106 209 1195 161 
Q 1284 113 1503 113 
L 1503 0 
L 84 0 
L 84 113 
Q 275 113 384 172 
Q 447 203 484 290 
Q 522 378 522 647 
L 522 3238 
Q 522 3728 498 3840 
Q 475 3953 426 3993 
Q 378 4034 297 4034 
Q 231 4034 84 3984 
L 41 4094 
L 897 4444 
L 1041 4444 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#TimesNewRomanPSMT-45"/>
      <use xlink:href="#TimesNewRomanPSMT-70" x="61.083984"/>
      <use xlink:href="#TimesNewRomanPSMT-6f" x="111.083984"/>
      <use xlink:href="#TimesNewRomanPSMT-63" x="161.083984"/>
      <use xlink:href="#TimesNewRomanPSMT-68" x="205.46875"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_7">
      <defs>
       <path id="m60947a5cb7" d="M 0 0 
L -3.5 0 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m60947a5cb7" x="57.6" y="279.289671" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_8">
      <!-- 0.1 -->
      <g transform="translate(33.1 284.150296) scale(0.14 -0.14)">
       <defs>
        <path id="TimesNewRomanPSMT-2e" d="M 800 606 
Q 947 606 1047 504 
Q 1147 403 1147 259 
Q 1147 116 1045 14 
Q 944 -88 800 -88 
Q 656 -88 554 14 
Q 453 116 453 259 
Q 453 406 554 506 
Q 656 606 800 606 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#TimesNewRomanPSMT-30"/>
       <use xlink:href="#TimesNewRomanPSMT-2e" x="50"/>
       <use xlink:href="#TimesNewRomanPSMT-31" x="75"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_8">
      <g>
       <use xlink:href="#m60947a5cb7" x="57.6" y="245.259568" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_9">
      <!-- 0.2 -->
      <g transform="translate(33.1 250.120193) scale(0.14 -0.14)">
       <use xlink:href="#TimesNewRomanPSMT-30"/>
       <use xlink:href="#TimesNewRomanPSMT-2e" x="50"/>
       <use xlink:href="#TimesNewRomanPSMT-32" x="75"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_9">
      <g>
       <use xlink:href="#m60947a5cb7" x="57.6" y="211.229466" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_10">
      <!-- 0.3 -->
      <g transform="translate(33.1 216.090091) scale(0.14 -0.14)">
       <use xlink:href="#TimesNewRomanPSMT-30"/>
       <use xlink:href="#TimesNewRomanPSMT-2e" x="50"/>
       <use xlink:href="#TimesNewRomanPSMT-33" x="75"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_10">
      <g>
       <use xlink:href="#m60947a5cb7" x="57.6" y="177.199363" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_11">
      <!-- 0.4 -->
      <g transform="translate(33.1 182.059988) scale(0.14 -0.14)">
       <use xlink:href="#TimesNewRomanPSMT-30"/>
       <use xlink:href="#TimesNewRomanPSMT-2e" x="50"/>
       <use xlink:href="#TimesNewRomanPSMT-34" x="75"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="line2d_11">
      <g>
       <use xlink:href="#m60947a5cb7" x="57.6" y="143.16926" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_12">
      <!-- 0.5 -->
      <g transform="translate(33.1 148.029885) scale(0.14 -0.14)">
       <use xlink:href="#TimesNewRomanPSMT-30"/>
       <use xlink:href="#TimesNewRomanPSMT-2e" x="50"/>
       <use xlink:href="#TimesNewRomanPSMT-35" x="75"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="line2d_12">
      <g>
       <use xlink:href="#m60947a5cb7" x="57.6" y="109.139158" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_13">
      <!-- 0.6 -->
      <g transform="translate(33.1 113.999783) scale(0.14 -0.14)">
       <defs>
        <path id="TimesNewRomanPSMT-36" d="M 2869 4325 
L 2869 4209 
Q 2456 4169 2195 4045 
Q 1934 3922 1679 3669 
Q 1425 3416 1258 3105 
Q 1091 2794 978 2366 
Q 1428 2675 1881 2675 
Q 2316 2675 2634 2325 
Q 2953 1975 2953 1425 
Q 2953 894 2631 456 
Q 2244 -75 1606 -75 
Q 1172 -75 869 213 
Q 275 772 275 1663 
Q 275 2231 503 2743 
Q 731 3256 1154 3653 
Q 1578 4050 1965 4187 
Q 2353 4325 2688 4325 
L 2869 4325 
z
M 925 2138 
Q 869 1716 869 1456 
Q 869 1156 980 804 
Q 1091 453 1309 247 
Q 1469 100 1697 100 
Q 1969 100 2183 356 
Q 2397 613 2397 1088 
Q 2397 1622 2184 2012 
Q 1972 2403 1581 2403 
Q 1463 2403 1327 2353 
Q 1191 2303 925 2138 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#TimesNewRomanPSMT-30"/>
       <use xlink:href="#TimesNewRomanPSMT-2e" x="50"/>
       <use xlink:href="#TimesNewRomanPSMT-36" x="75"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="line2d_13">
      <g>
       <use xlink:href="#m60947a5cb7" x="57.6" y="75.109055" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_14">
      <!-- 0.7 -->
      <g transform="translate(33.1 79.96968) scale(0.14 -0.14)">
       <defs>
        <path id="TimesNewRomanPSMT-37" d="M 644 4238 
L 2916 4238 
L 2916 4119 
L 1503 -88 
L 1153 -88 
L 2419 3728 
L 1253 3728 
Q 900 3728 750 3644 
Q 488 3500 328 3200 
L 238 3234 
L 644 4238 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#TimesNewRomanPSMT-30"/>
       <use xlink:href="#TimesNewRomanPSMT-2e" x="50"/>
       <use xlink:href="#TimesNewRomanPSMT-37" x="75"/>
      </g>
     </g>
    </g>
    <g id="text_15">
     <!-- Loss -->
     <g transform="translate(26.105313 187.752531) rotate(-90) scale(0.14 -0.14)">
      <defs>
       <path id="TimesNewRomanPSMT-4c" d="M 3669 1172 
L 3772 1150 
L 3409 0 
L 128 0 
L 128 116 
L 288 116 
Q 556 116 672 291 
Q 738 391 738 753 
L 738 3488 
Q 738 3884 650 3984 
Q 528 4122 288 4122 
L 128 4122 
L 128 4238 
L 2047 4238 
L 2047 4122 
Q 1709 4125 1573 4059 
Q 1438 3994 1388 3894 
Q 1338 3794 1338 3416 
L 1338 753 
Q 1338 494 1388 397 
Q 1425 331 1503 300 
Q 1581 269 1991 269 
L 2300 269 
Q 2788 269 2984 341 
Q 3181 413 3343 595 
Q 3506 778 3669 1172 
z
" transform="scale(0.015625)"/>
       <path id="TimesNewRomanPSMT-73" d="M 2050 2947 
L 2050 1972 
L 1947 1972 
Q 1828 2431 1642 2597 
Q 1456 2763 1169 2763 
Q 950 2763 815 2647 
Q 681 2531 681 2391 
Q 681 2216 781 2091 
Q 878 1963 1175 1819 
L 1631 1597 
Q 2266 1288 2266 781 
Q 2266 391 1970 151 
Q 1675 -88 1309 -88 
Q 1047 -88 709 6 
Q 606 38 541 38 
Q 469 38 428 -44 
L 325 -44 
L 325 978 
L 428 978 
Q 516 541 762 319 
Q 1009 97 1316 97 
Q 1531 97 1667 223 
Q 1803 350 1803 528 
Q 1803 744 1651 891 
Q 1500 1038 1047 1263 
Q 594 1488 453 1669 
Q 313 1847 313 2119 
Q 313 2472 555 2709 
Q 797 2947 1181 2947 
Q 1350 2947 1591 2875 
Q 1750 2828 1803 2828 
Q 1853 2828 1881 2850 
Q 1909 2872 1947 2947 
L 2050 2947 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#TimesNewRomanPSMT-4c"/>
      <use xlink:href="#TimesNewRomanPSMT-6f" x="61.083984"/>
      <use xlink:href="#TimesNewRomanPSMT-73" x="111.083984"/>
      <use xlink:href="#TimesNewRomanPSMT-73" x="150"/>
     </g>
    </g>
   </g>
   <g id="line2d_14">
    <path d="M 73.832727 55.950107 
L 74.483338 53.568 
L 75.133948 75.040995 
L 75.784558 128.706467 
L 76.435169 161.205215 
L 77.085779 179.207139 
L 77.736389 182.644179 
L 78.386999 194.656806 
L 79.03761 194.146354 
L 79.68822 207.656305 
L 80.33883 209.936322 
L 80.989441 220.791925 
L 81.640051 216.640252 
L 82.290661 221.948948 
L 82.941272 214.700536 
L 83.591882 212.55664 
L 84.242492 219.532811 
L 84.893103 224.943597 
L 86.194323 231.817678 
L 86.844934 229.775872 
L 87.495544 229.19736 
L 88.146154 237.738916 
L 88.796764 237.636825 
L 89.447375 235.76517 
L 90.097985 235.663079 
L 90.748595 232.974701 
L 91.399206 236.683983 
L 92.049816 242.196859 
L 94.001647 250.602295 
L 94.652257 246.382562 
L 95.953478 250.398114 
L 96.604088 251.929469 
L 97.254698 250.874535 
L 97.905309 253.120522 
L 98.555919 252.43992 
L 99.206529 255.57069 
L 99.85714 253.869184 
L 100.50775 246.756893 
L 101.15836 252.746191 
L 101.808971 250.840505 
L 102.459581 254.413666 
L 103.110191 254.379636 
L 103.760802 253.256643 
L 104.411412 255.196358 
L 105.062022 256.421442 
L 105.712633 256.625623 
L 106.363243 258.497278 
L 107.013853 255.945021 
L 107.664463 254.856057 
L 108.315074 255.536659 
L 108.965684 256.897864 
L 109.616294 255.57069 
L 110.266905 254.787997 
L 110.917515 253.290673 
L 111.568125 253.290673 
L 112.218736 257.033984 
L 112.869346 257.646526 
L 113.519956 257.272195 
L 114.170567 255.502629 
L 114.821177 256.387412 
L 115.471787 253.460823 
L 116.122398 247.539585 
L 116.773008 258.633399 
L 117.423618 253.801124 
L 118.074228 250.296024 
L 118.724839 257.374285 
L 119.375449 255.162328 
L 120.026059 256.251292 
L 120.67667 256.693683 
L 121.32728 257.578466 
L 121.97789 257.170104 
L 122.628501 257.884737 
L 123.279111 256.149201 
L 123.929721 257.204134 
L 124.580332 257.578466 
L 125.230942 258.293098 
L 125.881552 258.293098 
L 126.532163 258.156977 
L 127.182773 257.612496 
L 127.833383 257.306225 
L 128.483993 257.646526 
L 129.134604 258.122947 
L 129.785214 259.722362 
L 130.435824 255.502629 
L 131.086435 256.693683 
L 131.737045 255.945021 
L 132.387655 255.979051 
L 133.038266 259.07579 
L 134.339486 260.675205 
L 134.990097 256.013081 
L 135.640707 256.217261 
L 136.291317 258.293098 
L 136.941927 258.293098 
L 137.592538 257.680556 
L 138.243148 255.87696 
L 138.893758 250.704385 
L 139.544369 259.211911 
L 140.194979 257.510405 
L 140.845589 257.340255 
L 141.4962 256.931894 
L 142.14681 253.699034 
L 142.79742 255.945021 
L 143.448031 258.769519 
L 144.098641 254.719937 
L 144.749251 258.837579 
L 145.399862 257.748616 
L 146.050472 256.115171 
L 146.701082 255.8089 
L 147.351692 259.279971 
L 148.002303 255.945021 
L 148.652913 261.355807 
L 149.303523 256.319352 
L 149.954134 255.434569 
L 150.604744 258.769519 
L 151.255354 256.659653 
L 151.905965 257.680556 
L 152.556575 252.780221 
L 153.207185 256.557563 
L 153.857796 255.264419 
L 154.508406 255.264419 
L 155.159016 256.659653 
L 155.809627 260.096693 
L 156.460237 258.599369 
L 157.110847 255.264419 
L 157.761457 259.00773 
L 158.412068 258.599369 
L 159.062678 259.245941 
L 159.713288 257.033984 
L 160.363899 260.334904 
L 161.014509 256.625623 
L 161.665119 255.60472 
L 162.31573 256.217261 
L 162.96634 257.510405 
L 163.61695 258.293098 
L 164.267561 257.578466 
L 164.918171 255.128298 
L 165.568781 258.054887 
L 166.219392 256.421442 
L 166.870002 261.457897 
L 167.520612 259.824452 
L 168.171222 259.688332 
L 168.821833 257.952797 
L 169.472443 257.204134 
L 170.123053 257.306225 
L 170.773664 258.293098 
L 171.424274 254.822027 
L 172.074884 255.366509 
L 172.725495 257.748616 
L 173.376105 256.217261 
L 174.026715 256.421442 
L 174.677326 258.361158 
L 175.327936 256.387412 
L 175.978546 254.856057 
L 176.629156 254.890087 
L 177.279767 259.382061 
L 177.930377 258.293098 
L 178.580987 259.07579 
L 179.231598 253.630974 
L 179.882208 258.020857 
L 180.532818 256.693683 
L 181.183429 260.368934 
L 181.834039 256.965924 
L 182.484649 257.510405 
L 183.13526 259.416091 
L 183.78587 257.442345 
L 184.43648 254.583817 
L 185.087091 253.086492 
L 185.737701 257.306225 
L 186.388311 258.565339 
L 187.038921 255.434569 
L 187.689532 255.366509 
L 188.340142 259.858482 
L 188.990752 261.287747 
L 189.641363 252.201709 
L 190.291973 257.578466 
L 190.942583 257.340255 
L 191.593194 259.960573 
L 192.243804 255.196358 
L 192.894414 257.340255 
L 193.545025 255.128298 
L 194.195635 255.468599 
L 194.846245 259.416091 
L 195.496856 254.583817 
L 196.147466 256.965924 
L 196.798076 258.463248 
L 197.448686 256.081141 
L 198.099297 258.497278 
L 198.749907 255.8089 
L 199.400517 257.102044 
L 200.051128 254.413666 
L 200.701738 259.14385 
L 201.352348 257.816676 
L 202.002959 257.306225 
L 202.653569 260.300874 
L 203.304179 259.382061 
L 203.95479 257.408315 
L 204.6054 254.890087 
L 205.25601 257.374285 
L 205.906621 257.238165 
L 206.557231 254.549786 
L 207.207841 259.07579 
L 207.858451 257.442345 
L 208.509062 258.633399 
L 209.159672 256.251292 
L 209.810282 256.081141 
L 210.460893 258.497278 
L 211.111503 255.8089 
L 211.762113 257.102044 
L 212.412724 254.413666 
L 213.063334 259.14385 
L 213.713944 257.816676 
L 214.364555 257.306225 
L 215.015165 260.300874 
L 215.665775 259.382061 
L 216.316385 257.408315 
L 216.966996 254.890087 
L 217.617606 257.374285 
L 218.268216 257.238165 
L 218.918827 254.549786 
L 219.569437 259.07579 
L 220.220047 257.442345 
L 220.870658 258.633399 
L 221.521268 256.251292 
L 222.171878 256.081141 
L 222.822489 258.497278 
L 223.473099 255.8089 
L 224.123709 257.102044 
L 224.77432 254.413666 
L 225.42493 259.14385 
L 226.07554 257.816676 
L 226.72615 257.306225 
L 227.376761 260.300874 
L 228.027371 259.382061 
L 228.677981 257.408315 
L 229.328592 254.890087 
L 229.979202 257.374285 
L 230.629812 257.238165 
L 231.280423 254.549786 
L 231.931033 259.07579 
L 232.581643 257.442345 
L 233.232254 258.633399 
L 233.882864 256.251292 
L 234.533474 256.081141 
L 235.184085 258.497278 
L 235.834695 255.8089 
L 236.485305 257.102044 
L 237.135915 254.413666 
L 237.786526 259.14385 
L 238.437136 257.816676 
L 239.087746 257.306225 
L 239.738357 260.300874 
L 240.388967 259.382061 
L 241.039577 257.408315 
L 241.690188 254.890087 
L 242.340798 257.374285 
L 242.991408 257.238165 
L 243.642019 254.549786 
L 244.292629 259.07579 
L 244.943239 257.442345 
L 245.59385 258.633399 
L 246.24446 256.251292 
L 246.89507 256.081141 
L 247.54568 258.497278 
L 248.196291 255.8089 
L 248.846901 257.102044 
L 249.497511 254.413666 
L 250.148122 259.14385 
L 250.798732 257.816676 
L 251.449342 257.306225 
L 252.099953 260.300874 
L 252.750563 255.264419 
L 253.401173 259.00773 
L 254.051784 258.599369 
L 254.702394 259.245941 
L 255.353004 257.033984 
L 256.003615 260.334904 
L 256.654225 256.625623 
L 257.304835 255.60472 
L 257.955445 256.217261 
L 258.606056 257.510405 
L 259.256666 258.293098 
L 259.907276 257.578466 
L 260.557887 255.128298 
L 261.208497 258.054887 
L 261.859107 256.421442 
L 262.509718 261.457897 
L 263.160328 259.824452 
L 263.810938 259.688332 
L 264.461549 257.952797 
L 265.112159 257.204134 
L 265.762769 257.306225 
L 266.413379 258.293098 
L 267.06399 254.822027 
L 267.7146 255.366509 
L 268.36521 257.748616 
L 269.015821 256.217261 
L 269.666431 256.421442 
L 270.317041 258.361158 
L 270.967652 256.387412 
L 271.618262 254.856057 
L 272.268872 254.890087 
L 272.919483 259.382061 
L 273.570093 258.293098 
L 274.220703 259.07579 
L 274.871314 253.630974 
L 275.521924 258.020857 
L 276.172534 256.693683 
L 276.823144 260.368934 
L 277.473755 256.965924 
L 278.124365 257.510405 
L 278.774975 259.416091 
L 279.425586 257.442345 
L 280.076196 254.583817 
L 280.726806 253.086492 
L 281.377417 257.306225 
L 282.028027 258.565339 
L 282.678637 255.434569 
L 283.329248 255.366509 
L 283.979858 259.858482 
L 284.630468 261.287747 
L 285.281079 252.201709 
L 285.931689 257.578466 
L 286.582299 257.340255 
L 287.232909 259.960573 
L 287.88352 255.196358 
L 288.53413 257.340255 
L 289.18474 255.128298 
L 289.835351 255.468599 
L 290.485961 259.416091 
L 291.136571 254.583817 
L 291.787182 256.965924 
L 292.437792 258.463248 
L 293.088402 256.081141 
L 293.739013 258.497278 
L 294.389623 255.8089 
L 295.040233 257.102044 
L 295.690844 254.413666 
L 296.341454 259.14385 
L 296.992064 257.816676 
L 297.642674 257.306225 
L 298.293285 260.300874 
L 298.943895 259.382061 
L 299.594505 257.408315 
L 300.245116 254.890087 
L 300.895726 257.374285 
L 301.546336 257.238165 
L 302.196947 254.549786 
L 302.847557 259.07579 
L 303.498167 257.442345 
L 304.148778 258.633399 
L 304.799388 256.251292 
L 305.449998 256.081141 
L 306.100608 258.497278 
L 306.751219 255.8089 
L 307.401829 257.102044 
L 308.052439 254.413666 
L 308.70305 259.14385 
L 309.35366 257.816676 
L 310.00427 257.306225 
L 310.654881 260.300874 
L 311.305491 259.382061 
L 311.956101 257.408315 
L 312.606712 254.890087 
L 313.257322 257.374285 
L 313.907932 257.238165 
L 314.558543 254.549786 
L 315.209153 259.07579 
L 315.859763 257.442345 
L 316.510373 258.633399 
L 317.160984 256.251292 
L 317.811594 256.081141 
L 318.462204 258.497278 
L 319.112815 255.8089 
L 319.763425 257.102044 
L 320.414035 254.413666 
L 321.064646 259.14385 
L 321.715256 257.816676 
L 322.365866 257.306225 
L 323.016477 260.300874 
L 323.667087 259.382061 
L 324.317697 257.408315 
L 324.968308 254.890087 
L 325.618918 257.374285 
L 326.269528 257.238165 
L 326.920138 254.549786 
L 327.570749 259.07579 
L 328.221359 257.442345 
L 328.871969 258.633399 
L 329.52258 256.251292 
L 330.17319 256.081141 
L 330.8238 258.497278 
L 331.474411 255.8089 
L 332.125021 257.102044 
L 332.775631 254.413666 
L 333.426242 259.14385 
L 334.076852 257.816676 
L 334.727462 257.306225 
L 335.378073 260.300874 
L 336.028683 259.382061 
L 336.679293 257.408315 
L 337.329903 254.890087 
L 337.980514 257.374285 
L 338.631124 257.238165 
L 339.281734 254.549786 
L 339.932345 259.07579 
L 340.582955 257.442345 
L 341.233565 258.633399 
L 341.884176 256.251292 
L 342.534786 256.081141 
L 343.185396 258.497278 
L 343.836007 255.8089 
L 344.486617 257.102044 
L 345.137227 254.413666 
L 345.787837 259.14385 
L 346.438448 257.816676 
L 347.089058 257.306225 
L 347.739668 260.300874 
L 348.390279 255.264419 
L 349.040889 259.00773 
L 349.691499 258.599369 
L 350.34211 259.245941 
L 350.99272 257.033984 
L 351.64333 260.334904 
L 352.293941 256.625623 
L 352.944551 255.60472 
L 353.595161 256.217261 
L 354.245772 257.510405 
L 354.896382 258.293098 
L 355.546992 257.578466 
L 356.197602 255.128298 
L 356.848213 258.054887 
L 357.498823 256.421442 
L 358.149433 261.457897 
L 358.800044 259.824452 
L 359.450654 259.688332 
L 360.101264 257.952797 
L 360.751875 257.204134 
L 361.402485 257.306225 
L 362.053095 258.293098 
L 362.703706 254.822027 
L 363.354316 255.366509 
L 364.004926 257.748616 
L 364.655537 256.217261 
L 365.306147 256.421442 
L 365.956757 258.361158 
L 366.607367 256.387412 
L 367.257978 254.856057 
L 367.908588 254.890087 
L 368.559198 259.382061 
L 369.209809 258.293098 
L 369.860419 259.07579 
L 370.511029 253.630974 
L 371.16164 258.020857 
L 371.81225 256.693683 
L 372.46286 260.368934 
L 373.113471 256.965924 
L 373.764081 257.510405 
L 374.414691 259.416091 
L 375.065302 257.442345 
L 375.715912 254.583817 
L 376.366522 253.086492 
L 377.017132 257.306225 
L 377.667743 258.565339 
L 378.318353 255.434569 
L 378.968963 255.366509 
L 379.619574 259.858482 
L 380.270184 261.287747 
L 380.920794 252.201709 
L 381.571405 257.578466 
L 382.222015 257.340255 
L 382.872625 259.960573 
L 383.523236 255.196358 
L 384.173846 257.340255 
L 384.824456 255.128298 
L 385.475066 255.468599 
L 386.125677 259.416091 
L 386.776287 254.583817 
L 387.426897 256.965924 
L 388.077508 258.463248 
L 388.728118 256.081141 
L 389.378728 258.497278 
L 390.029339 255.8089 
L 390.679949 257.102044 
L 391.330559 254.413666 
L 391.98117 259.14385 
L 392.63178 257.816676 
L 393.28239 257.306225 
L 393.933001 260.300874 
L 394.583611 259.382061 
L 395.234221 257.408315 
L 395.884831 254.890087 
L 396.535442 257.374285 
L 397.186052 257.238165 
L 397.836662 254.549786 
L 398.487273 259.07579 
L 398.487273 259.07579 
" clip-path="url(#p3f8b87eb2b)" style="fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_15">
    <path d="M 73.832727 61.905375 
L 74.483338 56.494589 
L 75.133948 63.845091 
L 75.784558 67.520342 
L 76.435169 64.900024 
L 77.085779 78.239824 
L 77.736389 77.355042 
L 78.386999 73.61173 
L 79.03761 82.187316 
L 79.68822 83.48046 
L 80.33883 82.017166 
L 80.989441 83.37837 
L 81.640051 91.511564 
L 82.290661 91.375444 
L 82.941272 81.812985 
L 83.591882 93.689491 
L 84.242492 95.220846 
L 84.893103 94.438153 
L 85.543713 99.88297 
L 86.194323 101.618505 
L 86.844934 99.406548 
L 87.495544 95.765327 
L 88.146154 107.369592 
L 88.796764 109.037067 
L 89.447375 109.445429 
L 90.097985 111.555295 
L 90.748595 99.406548 
L 91.399206 117.034142 
L 92.049816 119.246098 
L 92.700426 116.319509 
L 93.351037 110.908723 
L 94.001647 94.472183 
L 94.652257 112.576198 
L 95.302868 114.209643 
L 95.953478 116.251449 
L 96.604088 117.204292 
L 97.254698 145.449277 
L 97.905309 146.50421 
L 98.555919 145.721518 
L 99.206529 137.384143 
L 99.85714 134.968006 
L 100.50775 141.808056 
L 101.15836 153.820683 
L 101.808971 156.713241 
L 102.459581 156.611151 
L 103.110191 141.876116 
L 103.760802 152.731719 
L 104.411412 158.789078 
L 105.062022 167.500784 
L 105.712633 168.317506 
L 106.363243 168.623777 
L 107.013853 174.136654 
L 107.664463 180.670434 
L 108.315074 181.929547 
L 108.965684 185.502708 
L 109.616294 190.913494 
L 110.266905 187.340334 
L 110.917515 197.787575 
L 111.568125 205.9548 
L 112.218736 205.376288 
L 112.869346 213.917844 
L 113.519956 213.815754 
L 114.170567 205.138077 
L 114.821177 215.245018 
L 115.471787 217.729215 
L 116.122398 213.577543 
L 116.773008 223.378212 
L 117.423618 222.56149 
L 118.074228 231.511407 
L 118.724839 229.980052 
L 119.375449 228.108397 
L 120.026059 233.859484 
L 120.67667 232.702461 
L 121.32728 238.827879 
L 121.97789 238.555638 
L 122.628501 240.257143 
L 123.929721 252.746191 
L 124.580332 250.840505 
L 125.230942 254.413666 
L 125.881552 254.379636 
L 127.182773 258.599369 
L 127.833383 256.421442 
L 128.483993 256.625623 
L 129.134604 261.900289 
L 129.785214 259.348031 
L 130.435824 261.662078 
L 131.086435 262.34268 
L 131.737045 263.703884 
L 132.387655 262.37671 
L 133.038266 271.803049 
L 133.688876 270.305724 
L 134.339486 270.305724 
L 134.990097 274.049035 
L 135.640707 274.661577 
L 136.291317 274.287246 
L 136.941927 275.920691 
L 137.592538 276.805474 
L 138.243148 277.281895 
L 138.893758 281.569688 
L 139.544369 282.454471 
L 140.194979 281.025206 
L 140.845589 280.923116 
L 141.4962 284.598367 
L 142.14681 285.789421 
L 142.79742 283.475374 
L 143.448031 283.917765 
L 144.098641 288.205558 
L 144.749251 287.797197 
L 145.399862 288.511829 
L 146.050472 286.776294 
L 146.701082 291.234237 
L 147.351692 291.608568 
L 148.002303 288.92019 
L 148.652913 288.92019 
L 149.303523 288.78407 
L 149.954134 291.642598 
L 150.604744 291.336327 
L 151.255354 291.676628 
L 151.905965 292.15305 
L 152.556575 293.752465 
L 153.207185 289.532732 
L 153.857796 290.723786 
L 154.508406 289.975123 
L 155.159016 290.009153 
L 155.809627 293.105893 
L 157.110847 294.705308 
L 157.761457 290.043184 
L 158.412068 290.247364 
L 159.062678 292.3232 
L 159.713288 292.3232 
L 160.363899 291.710659 
L 161.014509 289.907063 
L 161.665119 284.734488 
L 162.31573 293.242013 
L 162.96634 291.540508 
L 163.61695 291.370358 
L 164.267561 290.961996 
L 164.918171 287.729137 
L 165.568781 289.975123 
L 166.219392 292.799622 
L 166.870002 288.75004 
L 167.520612 292.867682 
L 168.171222 291.778719 
L 168.821833 290.145274 
L 169.472443 289.839003 
L 170.123053 293.310073 
L 170.773664 289.975123 
L 171.424274 295.38591 
L 172.074884 290.349454 
L 172.725495 289.464672 
L 173.376105 292.799622 
L 174.026715 290.689756 
L 174.677326 291.710659 
L 175.327936 286.810324 
L 175.978546 290.587665 
L 176.629156 289.294521 
L 177.279767 289.294521 
L 177.930377 290.689756 
L 178.580987 294.126796 
L 179.231598 292.629471 
L 179.882208 289.294521 
L 180.532818 293.037833 
L 181.183429 292.629471 
L 181.834039 293.276043 
L 182.484649 291.064087 
L 183.13526 294.365007 
L 183.78587 290.655725 
L 184.43648 289.634822 
L 185.087091 290.247364 
L 185.737701 291.540508 
L 186.388311 292.3232 
L 187.038921 291.608568 
L 187.689532 289.158401 
L 188.340142 292.08499 
L 188.990752 290.451545 
L 189.641363 295.488 
L 190.291973 293.854555 
L 190.942583 293.718435 
L 191.593194 291.982899 
L 192.243804 291.234237 
L 192.894414 291.336327 
L 193.545025 292.3232 
L 194.195635 288.85213 
L 194.846245 289.396612 
L 195.496856 291.778719 
L 196.147466 290.247364 
L 196.798076 290.451545 
L 197.448686 292.391261 
L 198.099297 290.417515 
L 198.749907 288.88616 
L 199.400517 288.92019 
L 200.051128 293.412164 
L 200.701738 292.3232 
L 201.352348 293.105893 
L 202.002959 287.661076 
L 202.653569 292.05096 
L 203.304179 290.723786 
L 203.95479 294.399037 
L 204.6054 290.996026 
L 205.25601 291.540508 
L 205.906621 293.446194 
L 206.557231 291.472448 
L 207.207841 288.613919 
L 207.858451 287.116595 
L 208.509062 291.336327 
L 209.159672 292.595441 
L 209.810282 289.464672 
L 210.460893 289.396612 
L 211.111503 293.888585 
L 211.762113 295.317849 
L 212.412724 286.231812 
L 213.063334 291.608568 
L 213.713944 291.370358 
L 214.364555 293.990675 
L 215.015165 289.226461 
L 215.665775 291.370358 
L 216.316385 289.158401 
L 216.966996 289.498702 
L 217.617606 293.446194 
L 218.268216 288.613919 
L 218.918827 290.996026 
L 219.569437 292.493351 
L 220.220047 290.111244 
L 220.870658 292.527381 
L 221.521268 289.839003 
L 222.171878 291.132147 
L 222.822489 288.443769 
L 223.473099 293.173953 
L 224.123709 291.846779 
L 224.77432 291.336327 
L 225.42493 294.330977 
L 226.07554 293.412164 
L 226.72615 291.438418 
L 227.376761 293.922615 
L 229.328592 293.922615 
L 229.979202 293.105893 
L 230.629812 291.472448 
L 231.280423 292.663501 
L 231.931033 290.281394 
L 232.581643 289.770943 
L 233.232254 289.770943 
L 233.882864 292.833652 
L 234.533474 293.582314 
L 235.184085 292.799622 
L 235.834695 290.009153 
L 236.485305 291.914839 
L 237.135915 288.171528 
L 237.786526 291.064087 
L 238.437136 291.540508 
L 239.087746 291.812749 
L 239.738357 293.922615 
L 242.991408 293.922615 
L 243.642019 289.668852 
L 244.292629 290.111244 
L 244.943239 289.770943 
L 245.59385 289.804973 
L 246.24446 290.553635 
L 246.89507 291.472448 
L 247.54568 292.867682 
L 248.196291 293.650374 
L 248.846901 290.655725 
L 249.497511 291.336327 
L 250.148122 290.825876 
L 250.798732 295.35188 
L 251.449342 288.273618 
L 252.750563 292.765592 
L 253.401173 290.145274 
L 254.051784 290.451545 
L 254.702394 293.922615 
L 259.256666 293.922615 
L 259.907276 287.797197 
L 261.208497 294.024706 
L 261.859107 292.595441 
L 262.509718 289.668852 
L 263.160328 289.634822 
L 263.810938 293.276043 
L 264.461549 292.05096 
L 265.112159 293.276043 
L 265.762769 290.247364 
L 266.413379 289.192431 
L 267.06399 290.655725 
L 267.7146 291.336327 
L 268.36521 290.825876 
L 269.015821 295.35188 
L 269.666431 288.273618 
L 270.967652 292.765592 
L 271.618262 290.145274 
L 272.268872 293.922615 
L 276.823144 293.922615 
L 277.473755 292.969772 
L 278.124365 287.797197 
L 279.425586 294.024706 
L 280.076196 292.595441 
L 280.726806 289.668852 
L 281.377417 289.634822 
L 282.028027 293.922615 
L 289.835351 293.922615 
L 290.485961 290.417515 
L 291.136571 292.765592 
L 291.787182 290.145274 
L 292.437792 290.451545 
L 293.088402 290.009153 
L 293.739013 290.519605 
L 294.389623 293.922615 
L 302.847557 293.922615 
L 303.498167 290.417515 
L 304.148778 292.765592 
L 304.799388 290.145274 
L 305.449998 293.922615 
L 398.487273 293.922615 
L 398.487273 293.922615 
" clip-path="url(#p3f8b87eb2b)" style="fill: none; stroke: #ff7f0e; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="patch_3">
    <path d="M 57.6 307.584 
L 57.6 41.472 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_4">
    <path d="M 414.72 307.584 
L 414.72 41.472 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_5">
    <path d="M 57.6 307.584 
L 414.72 307.584 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_6">
    <path d="M 57.6 41.472 
L 414.72 41.472 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_16">
    <!-- Model Loss -->
    <g transform="translate(196.258688 35.472) scale(0.168 -0.168)">
     <defs>
      <path id="TimesNewRomanPSMT-4d" d="M 2619 0 
L 981 3566 
L 981 734 
Q 981 344 1066 247 
Q 1181 116 1431 116 
L 1581 116 
L 1581 0 
L 106 0 
L 106 116 
L 256 116 
Q 525 116 638 278 
Q 706 378 706 734 
L 706 3503 
Q 706 3784 644 3909 
Q 600 4000 483 4061 
Q 366 4122 106 4122 
L 106 4238 
L 1306 4238 
L 2844 922 
L 4356 4238 
L 5556 4238 
L 5556 4122 
L 5409 4122 
Q 5138 4122 5025 3959 
Q 4956 3859 4956 3503 
L 4956 734 
Q 4956 344 5044 247 
Q 5159 116 5409 116 
L 5556 116 
L 5556 0 
L 3756 0 
L 3756 116 
L 3906 116 
Q 4178 116 4288 278 
Q 4356 378 4356 734 
L 4356 3566 
L 2722 0 
L 2619 0 
z
" transform="scale(0.015625)"/>
      <path id="TimesNewRomanPSMT-64" d="M 2222 322 
Q 2013 103 1813 7 
Q 1613 -88 1381 -88 
Q 913 -88 563 304 
Q 213 697 213 1313 
Q 213 1928 600 2439 
Q 988 2950 1597 2950 
Q 1975 2950 2222 2709 
L 2222 3238 
Q 2222 3728 2198 3840 
Q 2175 3953 2125 3993 
Q 2075 4034 2000 4034 
Q 1919 4034 1784 3984 
L 1744 4094 
L 2597 4444 
L 2738 4444 
L 2738 1134 
Q 2738 631 2761 520 
Q 2784 409 2836 365 
Q 2888 322 2956 322 
Q 3041 322 3181 375 
L 3216 266 
L 2366 -88 
L 2222 -88 
L 2222 322 
z
M 2222 541 
L 2222 2016 
Q 2203 2228 2109 2403 
Q 2016 2578 1861 2667 
Q 1706 2756 1559 2756 
Q 1284 2756 1069 2509 
Q 784 2184 784 1559 
Q 784 928 1059 592 
Q 1334 256 1672 256 
Q 1956 256 2222 541 
z
" transform="scale(0.015625)"/>
      <path id="TimesNewRomanPSMT-65" d="M 681 1784 
Q 678 1147 991 784 
Q 1303 422 1725 422 
Q 2006 422 2214 576 
Q 2422 731 2563 1106 
L 2659 1044 
Q 2594 616 2278 264 
Q 1963 -88 1488 -88 
Q 972 -88 605 314 
Q 238 716 238 1394 
Q 238 2128 614 2539 
Q 991 2950 1559 2950 
Q 2041 2950 2350 2633 
Q 2659 2316 2659 1784 
L 681 1784 
z
M 681 1966 
L 2006 1966 
Q 1991 2241 1941 2353 
Q 1863 2528 1708 2628 
Q 1553 2728 1384 2728 
Q 1125 2728 920 2526 
Q 716 2325 681 1966 
z
" transform="scale(0.015625)"/>
      <path id="TimesNewRomanPSMT-6c" d="M 1184 4444 
L 1184 647 
Q 1184 378 1223 290 
Q 1263 203 1344 158 
Q 1425 113 1647 113 
L 1647 0 
L 244 0 
L 244 113 
Q 441 113 512 153 
Q 584 194 625 287 
Q 666 381 666 647 
L 666 3247 
Q 666 3731 644 3842 
Q 622 3953 573 3993 
Q 525 4034 450 4034 
Q 369 4034 244 3984 
L 191 4094 
L 1044 4444 
L 1184 4444 
z
" transform="scale(0.015625)"/>
      <path id="TimesNewRomanPSMT-20" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#TimesNewRomanPSMT-4d"/>
     <use xlink:href="#TimesNewRomanPSMT-6f" x="88.916016"/>
     <use xlink:href="#TimesNewRomanPSMT-64" x="138.916016"/>
     <use xlink:href="#TimesNewRomanPSMT-65" x="188.916016"/>
     <use xlink:href="#TimesNewRomanPSMT-6c" x="233.300781"/>
     <use xlink:href="#TimesNewRomanPSMT-20" x="261.083984"/>
     <use xlink:href="#TimesNewRomanPSMT-4c" x="286.083984"/>
     <use xlink:href="#TimesNewRomanPSMT-6f" x="347.167969"/>
     <use xlink:href="#TimesNewRomanPSMT-73" x="397.167969"/>
     <use xlink:href="#TimesNewRomanPSMT-73" x="436.083984"/>
    </g>
   </g>
   <g id="legend_1">
    <g id="patch_7">
     <path d="M 302.58 92.287625 
L 404.92 92.287625 
Q 407.72 92.287625 407.72 89.487625 
L 407.72 51.272 
Q 407.72 48.472 404.92 48.472 
L 302.58 48.472 
Q 299.78 48.472 299.78 51.272 
L 299.78 89.487625 
Q 299.78 92.287625 302.58 92.287625 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="line2d_16">
     <path d="M 305.38 58.972 
L 319.38 58.972 
L 333.38 58.972 
" style="fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square"/>
    </g>
    <g id="text_17">
     <!-- Training -->
     <g transform="translate(344.58 63.872) scale(0.14 -0.14)">
      <defs>
       <path id="TimesNewRomanPSMT-54" d="M 3703 4238 
L 3750 3244 
L 3631 3244 
Q 3597 3506 3538 3619 
Q 3441 3800 3280 3886 
Q 3119 3972 2856 3972 
L 2259 3972 
L 2259 734 
Q 2259 344 2344 247 
Q 2463 116 2709 116 
L 2856 116 
L 2856 0 
L 1059 0 
L 1059 116 
L 1209 116 
Q 1478 116 1591 278 
Q 1659 378 1659 734 
L 1659 3972 
L 1150 3972 
Q 853 3972 728 3928 
Q 566 3869 450 3700 
Q 334 3531 313 3244 
L 194 3244 
L 244 4238 
L 3703 4238 
z
" transform="scale(0.015625)"/>
       <path id="TimesNewRomanPSMT-72" d="M 1038 2947 
L 1038 2303 
Q 1397 2947 1775 2947 
Q 1947 2947 2059 2842 
Q 2172 2738 2172 2600 
Q 2172 2478 2090 2393 
Q 2009 2309 1897 2309 
Q 1788 2309 1652 2417 
Q 1516 2525 1450 2525 
Q 1394 2525 1328 2463 
Q 1188 2334 1038 2041 
L 1038 669 
Q 1038 431 1097 309 
Q 1138 225 1241 169 
Q 1344 113 1538 113 
L 1538 0 
L 72 0 
L 72 113 
Q 291 113 397 181 
Q 475 231 506 341 
Q 522 394 522 644 
L 522 1753 
Q 522 2253 501 2348 
Q 481 2444 426 2487 
Q 372 2531 291 2531 
Q 194 2531 72 2484 
L 41 2597 
L 906 2947 
L 1038 2947 
z
" transform="scale(0.015625)"/>
       <path id="TimesNewRomanPSMT-61" d="M 1822 413 
Q 1381 72 1269 19 
Q 1100 -59 909 -59 
Q 613 -59 420 144 
Q 228 347 228 678 
Q 228 888 322 1041 
Q 450 1253 767 1440 
Q 1084 1628 1822 1897 
L 1822 2009 
Q 1822 2438 1686 2597 
Q 1550 2756 1291 2756 
Q 1094 2756 978 2650 
Q 859 2544 859 2406 
L 866 2225 
Q 866 2081 792 2003 
Q 719 1925 600 1925 
Q 484 1925 411 2006 
Q 338 2088 338 2228 
Q 338 2497 613 2722 
Q 888 2947 1384 2947 
Q 1766 2947 2009 2819 
Q 2194 2722 2281 2516 
Q 2338 2381 2338 1966 
L 2338 994 
Q 2338 584 2353 492 
Q 2369 400 2405 369 
Q 2441 338 2488 338 
Q 2538 338 2575 359 
Q 2641 400 2828 588 
L 2828 413 
Q 2478 -56 2159 -56 
Q 2006 -56 1915 50 
Q 1825 156 1822 413 
z
M 1822 616 
L 1822 1706 
Q 1350 1519 1213 1441 
Q 966 1303 859 1153 
Q 753 1003 753 825 
Q 753 600 887 451 
Q 1022 303 1197 303 
Q 1434 303 1822 616 
z
" transform="scale(0.015625)"/>
       <path id="TimesNewRomanPSMT-69" d="M 928 4444 
Q 1059 4444 1151 4351 
Q 1244 4259 1244 4128 
Q 1244 3997 1151 3903 
Q 1059 3809 928 3809 
Q 797 3809 703 3903 
Q 609 3997 609 4128 
Q 609 4259 701 4351 
Q 794 4444 928 4444 
z
M 1188 2947 
L 1188 647 
Q 1188 378 1227 289 
Q 1266 200 1342 156 
Q 1419 113 1622 113 
L 1622 0 
L 231 0 
L 231 113 
Q 441 113 512 153 
Q 584 194 626 287 
Q 669 381 669 647 
L 669 1750 
Q 669 2216 641 2353 
Q 619 2453 572 2492 
Q 525 2531 444 2531 
Q 356 2531 231 2484 
L 188 2597 
L 1050 2947 
L 1188 2947 
z
" transform="scale(0.015625)"/>
       <path id="TimesNewRomanPSMT-6e" d="M 1034 2341 
Q 1538 2947 1994 2947 
Q 2228 2947 2397 2830 
Q 2566 2713 2666 2444 
Q 2734 2256 2734 1869 
L 2734 647 
Q 2734 375 2778 278 
Q 2813 200 2889 156 
Q 2966 113 3172 113 
L 3172 0 
L 1756 0 
L 1756 113 
L 1816 113 
Q 2016 113 2095 173 
Q 2175 234 2206 353 
Q 2219 400 2219 647 
L 2219 1819 
Q 2219 2209 2117 2386 
Q 2016 2563 1775 2563 
Q 1403 2563 1034 2156 
L 1034 647 
Q 1034 356 1069 288 
Q 1113 197 1189 155 
Q 1266 113 1500 113 
L 1500 0 
L 84 0 
L 84 113 
L 147 113 
Q 366 113 442 223 
Q 519 334 519 647 
L 519 1709 
Q 519 2225 495 2337 
Q 472 2450 423 2490 
Q 375 2531 294 2531 
Q 206 2531 84 2484 
L 38 2597 
L 900 2947 
L 1034 2947 
L 1034 2341 
z
" transform="scale(0.015625)"/>
       <path id="TimesNewRomanPSMT-67" d="M 966 1044 
Q 703 1172 562 1401 
Q 422 1631 422 1909 
Q 422 2334 742 2640 
Q 1063 2947 1563 2947 
Q 1972 2947 2272 2747 
L 2878 2747 
Q 3013 2747 3034 2739 
Q 3056 2731 3066 2713 
Q 3084 2684 3084 2613 
Q 3084 2531 3069 2500 
Q 3059 2484 3036 2475 
Q 3013 2466 2878 2466 
L 2506 2466 
Q 2681 2241 2681 1891 
Q 2681 1491 2375 1206 
Q 2069 922 1553 922 
Q 1341 922 1119 984 
Q 981 866 932 777 
Q 884 688 884 625 
Q 884 572 936 522 
Q 988 472 1138 450 
Q 1225 438 1575 428 
Q 2219 413 2409 384 
Q 2700 344 2873 169 
Q 3047 -6 3047 -263 
Q 3047 -616 2716 -925 
Q 2228 -1381 1444 -1381 
Q 841 -1381 425 -1109 
Q 191 -953 191 -784 
Q 191 -709 225 -634 
Q 278 -519 444 -313 
Q 466 -284 763 25 
Q 600 122 533 198 
Q 466 275 466 372 
Q 466 481 555 628 
Q 644 775 966 1044 
z
M 1509 2797 
Q 1278 2797 1122 2612 
Q 966 2428 966 2047 
Q 966 1553 1178 1281 
Q 1341 1075 1591 1075 
Q 1828 1075 1981 1253 
Q 2134 1431 2134 1813 
Q 2134 2309 1919 2591 
Q 1759 2797 1509 2797 
z
M 934 0 
Q 788 -159 713 -296 
Q 638 -434 638 -550 
Q 638 -700 819 -813 
Q 1131 -1006 1722 -1006 
Q 2284 -1006 2551 -807 
Q 2819 -609 2819 -384 
Q 2819 -222 2659 -153 
Q 2497 -84 2016 -72 
Q 1313 -53 934 0 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#TimesNewRomanPSMT-54"/>
      <use xlink:href="#TimesNewRomanPSMT-72" x="57.583984"/>
      <use xlink:href="#TimesNewRomanPSMT-61" x="90.884766"/>
      <use xlink:href="#TimesNewRomanPSMT-69" x="135.269531"/>
      <use xlink:href="#TimesNewRomanPSMT-6e" x="163.052734"/>
      <use xlink:href="#TimesNewRomanPSMT-69" x="213.052734"/>
      <use xlink:href="#TimesNewRomanPSMT-6e" x="240.835938"/>
      <use xlink:href="#TimesNewRomanPSMT-67" x="290.835938"/>
     </g>
    </g>
    <g id="line2d_17">
     <path d="M 305.38 78.792937 
L 319.38 78.792937 
L 333.38 78.792937 
" style="fill: none; stroke: #ff7f0e; stroke-width: 1.5; stroke-linecap: square"/>
    </g>
    <g id="text_18">
     <!-- Validation -->
     <g transform="translate(344.58 83.692937) scale(0.14 -0.14)">
      <defs>
       <path id="TimesNewRomanPSMT-56" d="M 4544 4238 
L 4544 4122 
Q 4319 4081 4203 3978 
Q 4038 3825 3909 3509 
L 2431 -97 
L 2316 -97 
L 728 3556 
Q 606 3838 556 3900 
Q 478 3997 364 4051 
Q 250 4106 56 4122 
L 56 4238 
L 1788 4238 
L 1788 4122 
Q 1494 4094 1406 4022 
Q 1319 3950 1319 3838 
Q 1319 3681 1463 3350 
L 2541 866 
L 3541 3319 
Q 3688 3681 3688 3822 
Q 3688 3913 3597 3995 
Q 3506 4078 3291 4113 
Q 3275 4116 3238 4122 
L 3238 4238 
L 4544 4238 
z
" transform="scale(0.015625)"/>
       <path id="TimesNewRomanPSMT-74" d="M 1031 3803 
L 1031 2863 
L 1700 2863 
L 1700 2644 
L 1031 2644 
L 1031 788 
Q 1031 509 1111 412 
Q 1191 316 1316 316 
Q 1419 316 1516 380 
Q 1613 444 1666 569 
L 1788 569 
Q 1678 263 1478 108 
Q 1278 -47 1066 -47 
Q 922 -47 784 33 
Q 647 113 581 261 
Q 516 409 516 719 
L 516 2644 
L 63 2644 
L 63 2747 
Q 234 2816 414 2980 
Q 594 3144 734 3369 
Q 806 3488 934 3803 
L 1031 3803 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#TimesNewRomanPSMT-56"/>
      <use xlink:href="#TimesNewRomanPSMT-61" x="61.091797"/>
      <use xlink:href="#TimesNewRomanPSMT-6c" x="105.476562"/>
      <use xlink:href="#TimesNewRomanPSMT-69" x="133.259766"/>
      <use xlink:href="#TimesNewRomanPSMT-64" x="161.042969"/>
      <use xlink:href="#TimesNewRomanPSMT-61" x="211.042969"/>
      <use xlink:href="#TimesNewRomanPSMT-74" x="255.427734"/>
      <use xlink:href="#TimesNewRomanPSMT-69" x="283.210938"/>
      <use xlink:href="#TimesNewRomanPSMT-6f" x="310.994141"/>
      <use xlink:href="#TimesNewRomanPSMT-6e" x="360.994141"/>
     </g>
    </g>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="p3f8b87eb2b">
   <rect x="57.6" y="41.472" width="357.12" height="266.112"/>
  </clipPath>
 </defs>
</svg>
