<svg width="2740" height="2121" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" overflow="hidden"><g transform="translate(-830 -177)"><path d="M2978.3 991.214 3696.28 1310.32" stroke="#505050" stroke-width="0.543672" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 991.214 3696.28 1469.87" stroke="#505050" stroke-width="1.32393" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 991.214 3696.28 1629.42" stroke="#505050" stroke-width="1.25278" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 991.214 3696.28 1788.97" stroke="#505050" stroke-width="1.54376" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 991.214 3696.28 1948.52" stroke="#505050" stroke-width="0.518582" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 991.214 3696.28 2108.07" stroke="#505050" stroke-width="0.630982" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 991.214 3696.28 2267.63" stroke="#505050" stroke-width="1.65881" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 991.214 3696.28 2427.18" stroke="#505050" stroke-width="1.83143" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 1150.77 3696.28 1310.32" stroke="#505050" stroke-width="1.93313" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 1150.77 3696.28 1469.87" stroke="#505050" stroke-width="0.132043" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 1150.77 3696.28 1629.42" stroke="#505050" stroke-width="1.97805" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 1150.77 3696.28 1788.97" stroke="#505050" stroke-width="0.478459" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 1150.77 3696.28 1948.52" stroke="#505050" stroke-width="0.032179" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 1150.77 3696.28 2108.07" stroke="#505050" stroke-width="0.0449556" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 1150.77 3696.28 2267.63" stroke="#505050" stroke-width="0.591932" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 1150.77 3696.28 2427.18" stroke="#505050" stroke-width="1.52365" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 1310.32 3696.28 1310.32" stroke="#505050" stroke-width="1.99077" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 1310.32 3696.28 1469.87" stroke="#505050" stroke-width="0.510342" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 1310.32 3696.28 1629.42" stroke="#505050" stroke-width="0.692529" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 1310.32 3696.28 1788.97" stroke="#505050" stroke-width="1.95027" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 1310.32 3696.28 1948.52" stroke="#505050" stroke-width="1.09679" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 1310.32 3696.28 2108.07" stroke="#505050" stroke-width="1.82353" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 1310.32 3696.28 2267.63" stroke="#505050" stroke-width="1.72046" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 1310.32 3696.28 2427.18" stroke="#505050" stroke-width="0.761424" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 1469.87 3696.28 1310.32" stroke="#505050" stroke-width="1.87875" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 1469.87 3696.28 1469.87" stroke="#505050" stroke-width="1.84627" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 1469.87 3696.28 1629.42" stroke="#505050" stroke-width="0.548702" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 1469.87 3696.28 1788.97" stroke="#505050" stroke-width="0.200533" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 1469.87 3696.28 1948.52" stroke="#505050" stroke-width="1.57234" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 1469.87 3696.28 2108.07" stroke="#505050" stroke-width="1.93087" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 1469.87 3696.28 2267.63" stroke="#505050" stroke-width="0.0244732" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 1469.87 3696.28 2427.18" stroke="#505050" stroke-width="0.913369" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 1629.42 3696.28 1310.32" stroke="#505050" stroke-width="0.143692" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 1629.42 3696.28 1469.87" stroke="#505050" stroke-width="0.719897" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 1629.42 3696.28 1629.42" stroke="#505050" stroke-width="1.36994" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 1629.42 3696.28 1788.97" stroke="#505050" stroke-width="0.0626248" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 1629.42 3696.28 1948.52" stroke="#505050" stroke-width="0.00522519" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 1629.42 3696.28 2108.07" stroke="#505050" stroke-width="1.83595" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 1629.42 3696.28 2267.63" stroke="#505050" stroke-width="0.779325" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 1629.42 3696.28 2427.18" stroke="#505050" stroke-width="1.18633" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 1788.97 3696.28 1310.32" stroke="#505050" stroke-width="1.00792" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 1788.97 3696.28 1469.87" stroke="#505050" stroke-width="0.0704496" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 1788.97 3696.28 1629.42" stroke="#505050" stroke-width="0.329316" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 1788.97 3696.28 1788.97" stroke="#505050" stroke-width="1.97597" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 1788.97 3696.28 1948.52" stroke="#505050" stroke-width="1.12842" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 1788.97 3696.28 2108.07" stroke="#505050" stroke-width="0.598362" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 1788.97 3696.28 2267.63" stroke="#505050" stroke-width="1.07333" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 1788.97 3696.28 2427.18" stroke="#505050" stroke-width="0.582132" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 1948.52 3696.28 1310.32" stroke="#505050" stroke-width="0.339198" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 1948.52 3696.28 1469.87" stroke="#505050" stroke-width="1.96835" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 1948.52 3696.28 1629.42" stroke="#505050" stroke-width="1.67364" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 1948.52 3696.28 1788.97" stroke="#505050" stroke-width="1.09608" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 1948.52 3696.28 1948.52" stroke="#505050" stroke-width="1.06046" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 1948.52 3696.28 2108.07" stroke="#505050" stroke-width="1.90795" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 1948.52 3696.28 2267.63" stroke="#505050" stroke-width="1.65471" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 1948.52 3696.28 2427.18" stroke="#505050" stroke-width="1.372" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 2108.07 3696.28 1310.32" stroke="#505050" stroke-width="0.974533" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 2108.07 3696.28 1469.87" stroke="#505050" stroke-width="0.424758" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 2108.07 3696.28 1629.42" stroke="#505050" stroke-width="1.75098" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 2108.07 3696.28 1788.97" stroke="#505050" stroke-width="0.0974453" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 2108.07 3696.28 1948.52" stroke="#505050" stroke-width="0.0865455" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 2108.07 3696.28 2108.07" stroke="#505050" stroke-width="1.73776" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 2108.07 3696.28 2267.63" stroke="#505050" stroke-width="1.05697" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 2108.07 3696.28 2427.18" stroke="#505050" stroke-width="0.507868" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 2267.63 3696.28 1310.32" stroke="#505050" stroke-width="1.0042" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 2267.63 3696.28 1469.87" stroke="#505050" stroke-width="0.31529" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 2267.63 3696.28 1629.42" stroke="#505050" stroke-width="0.848016" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 2267.63 3696.28 1788.97" stroke="#505050" stroke-width="1.0792" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 2267.63 3696.28 1948.52" stroke="#505050" stroke-width="0.0138399" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 2267.63 3696.28 2108.07" stroke="#505050" stroke-width="0.138615" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 2267.63 3696.28 2267.63" stroke="#505050" stroke-width="1.8772" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 2267.63 3696.28 2427.18" stroke="#505050" stroke-width="1.68032" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 2427.18 3696.28 1310.32" stroke="#505050" stroke-width="0.743973" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 2427.18 3696.28 1469.87" stroke="#505050" stroke-width="1.45742" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 2427.18 3696.28 1629.42" stroke="#505050" stroke-width="0.974613" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 2427.18 3696.28 1788.97" stroke="#505050" stroke-width="0.592666" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 2427.18 3696.28 1948.52" stroke="#505050" stroke-width="1.7898" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 2427.18 3696.28 2108.07" stroke="#505050" stroke-width="0.909767" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 2427.18 3696.28 2267.63" stroke="#505050" stroke-width="1.36833" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 2427.18 3696.28 2427.18" stroke="#505050" stroke-width="0.388864" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 2586.73 3696.28 1310.32" stroke="#505050" stroke-width="0.802919" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 2586.73 3696.28 1469.87" stroke="#505050" stroke-width="0.418982" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 2586.73 3696.28 1629.42" stroke="#505050" stroke-width="1.40312" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 2586.73 3696.28 1788.97" stroke="#505050" stroke-width="1.97226" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 2586.73 3696.28 1948.52" stroke="#505050" stroke-width="0.214712" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 2586.73 3696.28 2108.07" stroke="#505050" stroke-width="0.255973" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 2586.73 3696.28 2267.63" stroke="#505050" stroke-width="1.58676" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 2586.73 3696.28 2427.18" stroke="#505050" stroke-width="0.785476" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 2746.28 3696.28 1310.32" stroke="#505050" stroke-width="1.82136" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 2746.28 3696.28 1469.87" stroke="#505050" stroke-width="0.525228" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 2746.28 3696.28 1629.42" stroke="#505050" stroke-width="1.77355" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 2746.28 3696.28 1788.97" stroke="#505050" stroke-width="1.18746" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 2746.28 3696.28 1948.52" stroke="#505050" stroke-width="0.831694" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 2746.28 3696.28 2108.07" stroke="#505050" stroke-width="1.02263" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 2746.28 3696.28 2267.63" stroke="#505050" stroke-width="0.0256333" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M2978.3 2746.28 3696.28 2427.18" stroke="#505050" stroke-width="0.588777" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3696.28 1310.32 4414.26 1629.42" stroke="#505050" stroke-width="1.90449" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3696.28 1310.32 4414.26 1788.97" stroke="#505050" stroke-width="1.02162" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3696.28 1310.32 4414.26 1948.52" stroke="#505050" stroke-width="1.99042" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3696.28 1310.32 4414.26 2108.07" stroke="#505050" stroke-width="1.87803" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3696.28 1469.87 4414.26 1629.42" stroke="#505050" stroke-width="0.674807" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3696.28 1469.87 4414.26 1788.97" stroke="#505050" stroke-width="1.41776" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3696.28 1469.87 4414.26 1948.52" stroke="#505050" stroke-width="0.918" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3696.28 1469.87 4414.26 2108.07" stroke="#505050" stroke-width="0.845631" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3696.28 1629.42 4414.26 1629.42" stroke="#505050" stroke-width="0.275044" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3696.28 1629.42 4414.26 1788.97" stroke="#505050" stroke-width="0.117862" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3696.28 1629.42 4414.26 1948.52" stroke="#505050" stroke-width="0.680798" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3696.28 1629.42 4414.26 2108.07" stroke="#505050" stroke-width="0.392087" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3696.28 1788.97 4414.26 1629.42" stroke="#505050" stroke-width="0.0423063" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3696.28 1788.97 4414.26 1788.97" stroke="#505050" stroke-width="0.701177" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3696.28 1788.97 4414.26 1948.52" stroke="#505050" stroke-width="1.9922" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3696.28 1788.97 4414.26 2108.07" stroke="#505050" stroke-width="0.270585" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3696.28 1948.52 4414.26 1629.42" stroke="#505050" stroke-width="0.538423" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3696.28 1948.52 4414.26 1788.97" stroke="#505050" stroke-width="0.335109" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3696.28 1948.52 4414.26 1948.52" stroke="#505050" stroke-width="0.966743" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3696.28 1948.52 4414.26 2108.07" stroke="#505050" stroke-width="0.983631" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3696.28 2108.07 4414.26 1629.42" stroke="#505050" stroke-width="0.266449" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3696.28 2108.07 4414.26 1788.97" stroke="#505050" stroke-width="0.490489" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3696.28 2108.07 4414.26 1948.52" stroke="#505050" stroke-width="1.54709" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3696.28 2108.07 4414.26 2108.07" stroke="#505050" stroke-width="0.612933" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3696.28 2267.63 4414.26 1629.42" stroke="#505050" stroke-width="0.40714" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3696.28 2267.63 4414.26 1788.97" stroke="#505050" stroke-width="1.90146" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3696.28 2267.63 4414.26 1948.52" stroke="#505050" stroke-width="0.915918" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3696.28 2267.63 4414.26 2108.07" stroke="#505050" stroke-width="1.25315" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3696.28 2427.18 4414.26 1629.42" stroke="#505050" stroke-width="0.339037" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3696.28 2427.18 4414.26 1788.97" stroke="#505050" stroke-width="1.81935" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3696.28 2427.18 4414.26 1948.52" stroke="#505050" stroke-width="1.61355" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3696.28 2427.18 4414.26 2108.07" stroke="#505050" stroke-width="0.531235" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M4414.26 1629.42 5132.24 1868.75" stroke="#505050" stroke-width="1.59149" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M4414.26 1788.97 5132.24 1868.75" stroke="#505050" stroke-width="1.7283" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M4414.26 1948.52 5132.24 1868.75" stroke="#505050" stroke-width="1.00374" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M4414.26 2108.07 5132.24 1868.75" stroke="#505050" stroke-width="0.125112" fill="none" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3018.18 991.214C3018.18 1013.24 3000.32 1031.1 2978.3 1031.1 2956.27 1031.1 2938.41 1013.24 2938.41 991.214 2938.41 969.184 2956.27 951.326 2978.3 951.326 3000.32 951.326 3018.18 969.184 3018.18 991.214Z" stroke="#333333" stroke-width="3.98879" fill="#FFFFFF" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3018.18 1150.77C3018.18 1172.79 3000.32 1190.65 2978.3 1190.65 2956.27 1190.65 2938.41 1172.79 2938.41 1150.77 2938.41 1128.74 2956.27 1110.88 2978.3 1110.88 3000.32 1110.88 3018.18 1128.74 3018.18 1150.77Z" stroke="#333333" stroke-width="3.98879" fill="#FFFFFF" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3018.18 1310.32C3018.18 1332.35 3000.32 1350.2 2978.3 1350.2 2956.27 1350.2 2938.41 1332.35 2938.41 1310.32 2938.41 1288.29 2956.27 1270.43 2978.3 1270.43 3000.32 1270.43 3018.18 1288.29 3018.18 1310.32Z" stroke="#333333" stroke-width="3.98879" fill="#FFFFFF" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3018.18 1469.87C3018.18 1491.9 3000.32 1509.76 2978.3 1509.76 2956.27 1509.76 2938.41 1491.9 2938.41 1469.87 2938.41 1447.84 2956.27 1429.98 2978.3 1429.98 3000.32 1429.98 3018.18 1447.84 3018.18 1469.87Z" stroke="#333333" stroke-width="3.98879" fill="#FFFFFF" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3018.18 1629.42C3018.18 1651.45 3000.32 1669.31 2978.3 1669.31 2956.27 1669.31 2938.41 1651.45 2938.41 1629.42 2938.41 1607.39 2956.27 1589.53 2978.3 1589.53 3000.32 1589.53 3018.18 1607.39 3018.18 1629.42Z" stroke="#333333" stroke-width="3.98879" fill="#FFFFFF" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3018.18 1788.97C3018.18 1811 3000.32 1828.86 2978.3 1828.86 2956.27 1828.86 2938.41 1811 2938.41 1788.97 2938.41 1766.94 2956.27 1749.08 2978.3 1749.08 3000.32 1749.08 3018.18 1766.94 3018.18 1788.97Z" stroke="#333333" stroke-width="3.98879" fill="#FFFFFF" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3018.18 1948.52C3018.18 1970.55 3000.32 1988.41 2978.3 1988.41 2956.27 1988.41 2938.41 1970.55 2938.41 1948.52 2938.41 1926.49 2956.27 1908.64 2978.3 1908.64 3000.32 1908.64 3018.18 1926.49 3018.18 1948.52Z" stroke="#333333" stroke-width="3.98879" fill="#FFFFFF" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3018.18 2108.07C3018.18 2130.1 3000.32 2147.96 2978.3 2147.96 2956.27 2147.96 2938.41 2130.1 2938.41 2108.07 2938.41 2086.05 2956.27 2068.19 2978.3 2068.19 3000.32 2068.19 3018.18 2086.05 3018.18 2108.07Z" stroke="#333333" stroke-width="3.98879" fill="#FFFFFF" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3018.18 2267.63C3018.18 2289.66 3000.32 2307.51 2978.3 2307.51 2956.27 2307.51 2938.41 2289.66 2938.41 2267.63 2938.41 2245.6 2956.27 2227.74 2978.3 2227.74 3000.32 2227.74 3018.18 2245.6 3018.18 2267.63Z" stroke="#333333" stroke-width="3.98879" fill="#FFFFFF" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3018.18 2427.18C3018.18 2449.21 3000.32 2467.07 2978.3 2467.07 2956.27 2467.07 2938.41 2449.21 2938.41 2427.18 2938.41 2405.15 2956.27 2387.29 2978.3 2387.29 3000.32 2387.29 3018.18 2405.15 3018.18 2427.18Z" stroke="#333333" stroke-width="3.98879" fill="#FFFFFF" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3018.18 2586.73C3018.18 2608.76 3000.32 2626.62 2978.3 2626.62 2956.27 2626.62 2938.41 2608.76 2938.41 2586.73 2938.41 2564.7 2956.27 2546.84 2978.3 2546.84 3000.32 2546.84 3018.18 2564.7 3018.18 2586.73Z" stroke="#333333" stroke-width="3.98879" fill="#FFFFFF" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3018.18 2746.28C3018.18 2768.31 3000.32 2786.17 2978.3 2786.17 2956.27 2786.17 2938.41 2768.31 2938.41 2746.28 2938.41 2724.25 2956.27 2706.39 2978.3 2706.39 3000.32 2706.39 3018.18 2724.25 3018.18 2746.28Z" stroke="#333333" stroke-width="3.98879" fill="#FFFFFF" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3736.17 1310.32C3736.17 1332.35 3718.31 1350.2 3696.28 1350.2 3674.25 1350.2 3656.39 1332.35 3656.39 1310.32 3656.39 1288.29 3674.25 1270.43 3696.28 1270.43 3718.31 1270.43 3736.17 1288.29 3736.17 1310.32Z" stroke="#333333" stroke-width="3.98879" fill="#FFFFFF" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3736.17 1469.87C3736.17 1491.9 3718.31 1509.76 3696.28 1509.76 3674.25 1509.76 3656.39 1491.9 3656.39 1469.87 3656.39 1447.84 3674.25 1429.98 3696.28 1429.98 3718.31 1429.98 3736.17 1447.84 3736.17 1469.87Z" stroke="#333333" stroke-width="3.98879" fill="#FFFFFF" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3736.17 1629.42C3736.17 1651.45 3718.31 1669.31 3696.28 1669.31 3674.25 1669.31 3656.39 1651.45 3656.39 1629.42 3656.39 1607.39 3674.25 1589.53 3696.28 1589.53 3718.31 1589.53 3736.17 1607.39 3736.17 1629.42Z" stroke="#333333" stroke-width="3.98879" fill="#FFFFFF" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3736.17 1788.97C3736.17 1811 3718.31 1828.86 3696.28 1828.86 3674.25 1828.86 3656.39 1811 3656.39 1788.97 3656.39 1766.94 3674.25 1749.08 3696.28 1749.08 3718.31 1749.08 3736.17 1766.94 3736.17 1788.97Z" stroke="#333333" stroke-width="3.98879" fill="#FFFFFF" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3736.17 1948.52C3736.17 1970.55 3718.31 1988.41 3696.28 1988.41 3674.25 1988.41 3656.39 1970.55 3656.39 1948.52 3656.39 1926.49 3674.25 1908.64 3696.28 1908.64 3718.31 1908.64 3736.17 1926.49 3736.17 1948.52Z" stroke="#333333" stroke-width="3.98879" fill="#FFFFFF" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3736.17 2108.07C3736.17 2130.1 3718.31 2147.96 3696.28 2147.96 3674.25 2147.96 3656.39 2130.1 3656.39 2108.07 3656.39 2086.05 3674.25 2068.19 3696.28 2068.19 3718.31 2068.19 3736.17 2086.05 3736.17 2108.07Z" stroke="#333333" stroke-width="3.98879" fill="#FFFFFF" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3736.17 2267.63C3736.17 2289.66 3718.31 2307.51 3696.28 2307.51 3674.25 2307.51 3656.39 2289.66 3656.39 2267.63 3656.39 2245.6 3674.25 2227.74 3696.28 2227.74 3718.31 2227.74 3736.17 2245.6 3736.17 2267.63Z" stroke="#333333" stroke-width="3.98879" fill="#FFFFFF" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M3736.17 2427.18C3736.17 2449.21 3718.31 2467.07 3696.28 2467.07 3674.25 2467.07 3656.39 2449.21 3656.39 2427.18 3656.39 2405.15 3674.25 2387.29 3696.28 2387.29 3718.31 2387.29 3736.17 2405.15 3736.17 2427.18Z" stroke="#333333" stroke-width="3.98879" fill="#FFFFFF" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M4454.15 1629.42C4454.15 1651.45 4436.29 1669.31 4414.26 1669.31 4392.23 1669.31 4374.37 1651.45 4374.37 1629.42 4374.37 1607.39 4392.23 1589.53 4414.26 1589.53 4436.29 1589.53 4454.15 1607.39 4454.15 1629.42Z" stroke="#333333" stroke-width="3.98879" fill="#FFFFFF" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M4454.15 1788.97C4454.15 1811 4436.29 1828.86 4414.26 1828.86 4392.23 1828.86 4374.37 1811 4374.37 1788.97 4374.37 1766.94 4392.23 1749.08 4414.26 1749.08 4436.29 1749.08 4454.15 1766.94 4454.15 1788.97Z" stroke="#333333" stroke-width="3.98879" fill="#FFFFFF" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M4454.15 1948.52C4454.15 1970.55 4436.29 1988.41 4414.26 1988.41 4392.23 1988.41 4374.37 1970.55 4374.37 1948.52 4374.37 1926.49 4392.23 1908.64 4414.26 1908.64 4436.29 1908.64 4454.15 1926.49 4454.15 1948.52Z" stroke="#333333" stroke-width="3.98879" fill="#FFFFFF" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M4454.15 2108.07C4454.15 2130.1 4436.29 2147.96 4414.26 2147.96 4392.23 2147.96 4374.37 2130.1 4374.37 2108.07 4374.37 2086.05 4392.23 2068.19 4414.26 2068.19 4436.29 2068.19 4454.15 2086.05 4454.15 2108.07Z" stroke="#333333" stroke-width="3.98879" fill="#FFFFFF" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><path d="M5172.13 1868.75C5172.13 1890.78 5154.27 1908.64 5132.24 1908.64 5110.21 1908.64 5092.35 1890.78 5092.35 1868.75 5092.35 1846.72 5110.21 1828.86 5132.24 1828.86 5154.27 1828.86 5172.13 1846.72 5172.13 1868.75Z" stroke="#333333" stroke-width="3.98879" fill="#FFFFFF" transform="matrix(1 0 0 1.0007 -1868.98 -729.12)"/><text font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="12" transform="matrix(3.98879 0 0 3.99156 969.707 2195)">Input Layer </text><text font-family="Cambria Math,Cambria Math_MSFontService,sans-serif" font-weight="400" font-size="11" transform="matrix(3.98879 0 0 3.99156 1221 2195)">∈</text><text font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="12" transform="matrix(3.98879 0 0 3.99156 1248.92 2195)"> </text><text font-family="Cambria Math,Cambria Math_MSFontService,sans-serif" font-weight="400" font-size="11" transform="matrix(3.98879 0 0 3.99156 1260.89 2195)">ℝ</text><text font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="12" transform="matrix(3.98879 0 0 3.99156 1292.8 2195)">¹²</text><text font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="12" transform="matrix(3.98879 0 0 3.99156 1687.69 2195)">Hidden Layer </text><text font-family="Cambria Math,Cambria Math_MSFontService,sans-serif" font-weight="400" font-size="11" transform="matrix(3.98879 0 0 3.99156 1990.84 2195)">∈</text><text font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="12" transform="matrix(3.98879 0 0 3.99156 2018.76 2195)"> </text><text font-family="Cambria Math,Cambria Math_MSFontService,sans-serif" font-weight="400" font-size="11" transform="matrix(3.98879 0 0 3.99156 2030.72 2195)">ℝ</text><text font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="12" transform="matrix(3.98879 0 0 3.99156 2062.63 2195)">⁸</text><text font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="12" transform="matrix(3.98879 0 0 3.99156 2405.67 2195)">Hidden Layer </text><text font-family="Cambria Math,Cambria Math_MSFontService,sans-serif" font-weight="400" font-size="11" transform="matrix(3.98879 0 0 3.99156 2708.82 2195)">∈</text><text font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="12" transform="matrix(3.98879 0 0 3.99156 2736.74 2195)"> </text><text font-family="Cambria Math,Cambria Math_MSFontService,sans-serif" font-weight="400" font-size="11" transform="matrix(3.98879 0 0 3.99156 2748.71 2195)">ℝ</text><text font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="12" transform="matrix(3.98879 0 0 3.99156 2780.62 2195)">⁴</text><text font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="12" transform="matrix(3.98879 0 0 3.99156 3123.65 2195)">Output Layer </text><text font-family="Cambria Math,Cambria Math_MSFontService,sans-serif" font-weight="400" font-size="11" transform="matrix(3.98879 0 0 3.99156 3410.84 2195)">∈</text><text font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="12" transform="matrix(3.98879 0 0 3.99156 3438.77 2195)"> </text><text font-family="Cambria Math,Cambria Math_MSFontService,sans-serif" font-weight="400" font-size="11" transform="matrix(3.98879 0 0 3.99156 3450.73 2195)">ℝ</text><text font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="12" transform="matrix(3.98879 0 0 3.99156 3482.64 2195)">¹</text><rect x="1293" y="2150" width="35.0001" height="38.9998" fill="#FFFFFF"/><text font-family="Aptos,Aptos_MSFontService,sans-serif" font-weight="700" font-size="32" transform="matrix(1 0 0 1 1293.26 2180)">96</text><rect x="2062" y="2150" width="34.9998" height="38.9998" fill="#FFFFFF"/><text font-family="Aptos,Aptos_MSFontService,sans-serif" font-weight="700" font-size="32" transform="matrix(1 0 0 1 2062.41 2180)">64</text><rect x="2781" y="2150" width="34" height="38.9998" fill="#FFFFFF"/><text font-family="Aptos,Aptos_MSFontService,sans-serif" font-weight="700" font-size="32" transform="matrix(1 0 0 1 2780.84 2180)">32</text></g></svg>