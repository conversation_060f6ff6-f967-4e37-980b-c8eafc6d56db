%%%%%%%%%%%%%%%%%%%% author.tex %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%
% sample root file for your "contribution" to a contributed volume
%
% Use this file as a template for your own input.
%
%%%%%%%%%%%%%%%% Springer %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%


%% RECOMMENDED %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%\documentclass[graybox]{svmult}
%
%% choose options for [] as required from the list
%% in the Reference Guide
%
%\usepackage{mathptmx}       % selects Times Roman as basic font
%\usepackage{helvet}         % selects Helvetica as sans-serif font
%\usepackage{courier}        % selects Courier as typewriter font
%\usepackage{type1cm}        % activate if the above 3 fonts are
                             % not available on your system
%
%\usepackage{makeidx}         % allows index generation
%\usepackage{graphicx}        % standard LaTeX graphics tool
%                             % when including figure files
%\usepackage{multicol}        % used for the two-column index
%\usepackage[bottom]{footmisc}% places footnotes at page bottom
%
%% see the list of further useful packages
%% in the Reference Guide
%
%\makeindex             % used for the subject index
%                       % please use the style svind.ist with
%                       % your makeindex program
%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%
%\begin{document}






\newcommand{\tensor}[1]{\bm{\mathcal{#1}}}

\newcommand{\inp}[2]{\left\langle #1, #2 \right\rangle} 
\newcommand{\llangle}{\left\langle\hspace{-2.5pt}\left\langle} 
\newcommand{\rrangle}{\right\rangle\hspace{-2.5pt}\right\rangle} 


\newcommand{\Aref}[1]{Algorithm.~\ref{#1}}
\newcommand{\Sref}[1]{Sec.~\ref{#1}}

\newcommand{\matsui}[1]{\textbf{\textcolor{cyan}{[\textsc{MATSUI:} #1]}}}
\newcommand{\yokota}[1]{\textbf{\textcolor{magenta}{[\textsc{YOKOTA:} #1]}}}

\title{EM Algorithm for Tensor Network Logistic Regression based on P\'{o}lya-Gamma Augmentation}
% Use \titlerunning{Short Title} for an abbreviated version of
% your contribution title if the original one is too long
\author{Naoya Yamauchi, Yuta Mizuno, Hidekata Hontani, and Tatsuya Yokota\orcidID{0000-0002-7368-2060}}
% Use \authorrunning{Short Title} for an abbreviated version of
% your contribution title if the original one is too long
\institute{Naoya Yamauchi, Yuta Mizuno, Hidekata Hontani, Tatsuya Yokota \at Nagoya Institute of Technology, Aichi, Japan, 
\and Tatsuya Yokota \at RIKEN Center for Advanced Intelligence Project, Tokyo, Japan,  
\email{<EMAIL>}}
%
% Use the package "url.sty" to avoid
% problems with special characters
% used in your e-mail or web address
%
\maketitle

\abstract*{Each chapter should be preceded by an abstract (no more than 200 words) that summarizes the content. The abstract will appear \textit{online} at \url{www.SpringerLink.com} and be available with unrestricted access. This allows unregistered users to read the abstract as a teaser for the complete chapter.
Please use the 'starred' version of the \texttt{abstract} command for typesetting the text of the online abstracts (cf. source file of this chapter template \texttt{abstract}) and include them with the source files of your manuscript. Use the plain \texttt{abstract} command if the abstract is also to appear in the printed version of the book.}

\abstract{In recent years, a learning method for classifiers using tensor networks (tensor network regression~\cite{stoudenmire2016supervised}) has attracted attention.
Learning tensor networks (TN) is incompatible with the gradient method, and the alternating least squares (ALS) algorithm is widely used. However it is difficult to directly apply ALS to minimize logistic loss, and this makes learning the classifier uncomfortable. In this study, we propose a new algorithm to solve this problem and achieve efficient learning of tensor network logistic regression (TNLR) models. The key point of the proposed method is to iteratively minimize the auxiliary function instead of minimizing the logistic loss. The auxiliary function can be given as a weighted squared loss by employing the P\'{o}lya-Gamma (PG) augmentation, which allows the ALS algorithm to be applied. We apply the proposed method to the training of MNIST and Fashion MNIST classification and discuss the effectiveness of the proposed method.}

\section{Introduction}
Tensor network (TN) is a mathematical model in which a higher-order tensor is represented by the product of multiple lower-order tensors (see Figure~\ref{fig:TN}).
Recently, machine learning algorithms based on TN~\cite{wang2023tensor} have been studied such as compression of each layer of a neural network~\cite{novikov2015tensorizing}, regression models that directly use TN~\cite{stoudenmire2016supervised}, and generative models~\cite{novikov2021tensor}.
Since models described by TN can be implemented in quantum circuits~\cite{wall2022tensor}, they are expected to become the next generation of computational technology and are attracting attention as quantum machine learning~\cite{rieser2023tensor}.

In this study, we consider a regression model~\cite{stoudenmire2016supervised} that uses TN directly. A linear regression model for a feature $\mathbf x \in \mathbb{R}^{M}$ is given by $\langle \mathbf w, \mathbf x \rangle + v$ with coefficient $\mathbf w \in \mathbb{R}^{M}$ and bias $v \in \mathbb{R}$. 
On the other hand, the regression model with TN is given as 
\begin{align}
  \psi(\mathbf x) = \langle \tensor{W}, \mathbf \Phi(\mathbf x) \rangle + v
\end{align}
where $\mathbf \Phi: \mathbb{R}^M \rightarrow \mathbb{R}^{d_1 \times d_2 \times \cdots \times d_M} $ is the embedding of the feature $\mathbf x$ into higher-dimensional space and $\tensor{W} \in \mathbb{R}^{d_1 \times d_2 \times \cdots \times d_M}$ is the coefficient tensor. The literature~\cite{stoudenmire2016supervised} employs a mapping to a rank-1 tensor such that
\begin{align}
  \mathbf\Phi(\mathbf x) = \boldsymbol\phi(x_1) \otimes \boldsymbol\phi(x_2) \otimes \cdots \otimes\boldsymbol\phi(x_M) 
\end{align}
with the function $\boldsymbol\phi(x)= \left[\cos \frac{\pi}{2}x, \sin\frac{\pi}{2}x \right]^\top \in \mathbb{R}^2$.
Although $\mathbf \Phi(\bm x)$ is a large tensor with $2^M$ elements, it is not necessary to compute itself explicitly. Also, in the TN model, the large coefficient tensor $\tensor{W}$ can be expressed as Tensor-Train decomposition~\cite{oseledets2011tensor}:
\begin{align}
  \tensor{W} = \llangle \tensor{A}_1, \tensor{A}_2, ..., \tensor{A}_M \rrangle.
\end{align}
Each $\tensor{A}_j$ is a second or third order tensor called a core tensor. A large bond dimension $R$ of core tensors increases the number of parameters, and provides a high expressive power. In the TN model, $\tensor{W}$ does not need to be explicitly calculated, and the output can be obtained by contraction with $\mathbf \Phi(\bm x)$ once the core tensors are obtained. Figure \ref{fig:TN} shows the TN model expressed as a tensor diagram.

\begin{figure}[t]
\centering
\includegraphics[width=0.96\textwidth]{fig001/TNmodel.pdf}
\caption{TN and TN regression model}\label{fig:TN}
\end{figure}

The problem of learning TN based on squared errors with training data $\{(\mathbf x_n, y_n)\}_{n \in [N]}$ (TN least squares regression, TNLSR) is given by 
\begin{align}
\underset{\boldsymbol{\mathcal{A}}_1,\boldsymbol{\mathcal{A}}_2,\cdots,\boldsymbol{\mathcal{A}}_M, v} {\text{minimize}} & \ \ \sum_{n=1}^N(y_n-\langle{\tensor{W}, \mathbf\Phi(\mathbf x_n)}\rangle - v)^2, \label{eq:l2_loss}\\
\text{subject to} & \ \ \ \boldsymbol{\mathcal{W}}=\llangle{\boldsymbol{\mathcal{A}}_1,\boldsymbol{\mathcal{A}}_2,\cdots,\boldsymbol{\mathcal{A}}_M}\rrangle.
\end{align}

Let $\theta=\{\tensor{A}_1, ..., \tensor{A}_M, v\}$ denote the set of optimization parameters in Equation (\ref{eq:l2_loss}) and the squared error $f(\theta)$, the gradient descent algorithm is given by 
\begin{align}
\theta^{(t+1)} \leftarrow \theta^{(t)} - \mu^{(t)} \nabla_\theta f(\theta^{(t)})
\end{align}
where $\mu^{(t)}$ is the learning rate chosen adaptively at each step.
On the other hand, the alternating least squares algorithm (ALS) is given by 
\begin{align}
(\tensor{A}_m,v) \leftarrow \argmin_{\tensor{A}_m,v} f(\tensor{A}_m,v), m=1,...,M. \label{eq:Am}
\end{align}    
In (\ref{eq:Am}), the squared error is a quadratic function for the optimization parameter, and the update rule of this  sub-optimization is given by closed form, and the error is monotonically decreasing (non-increasing).
Figure \ref{fig:opt_l2} shows a comparison of the two algorithms.
These are the results of training a binary classifier by extracting two types of characters from MNIST.
The ALS algorithm converges very quickly and achieves a better optimal solution. The gradient descent algorithm, which searches the best learning rate\footnote[1]{If the learning rate is not adapted, the learning fails.} at each step, converges very slowly. 


In this study, we discuss an extension of TNLSR to logistic regression (TN logistic regression, TNLR). Logistic regression assumes binary labels $y_n \in \{0,1\}$. In this case, the squared loss is not appropriate, and we consider minimizing the sum of logistic loss $\sum_{n \in [N]} l_n$. The $n$-th contribution of logistic loss $l_n$ is given by
\begin{align}
l_n = - y_n \log \sigma(\psi(\mathbf x_n)) - (1-y_n) \log (1 - \sigma(\psi(\mathbf x_n))). \label{eq:logistic_loss}
\end{align}
Note that $\sigma(z) = 1/(1+\exp(-z))$ is a logistic function. When considering logistic loss, the gradient method can be applied directly, but its optimization is difficult as in the case of TNLSR.


In this study, we propose a new optimization algorithm to solve this problem.
Rather than minimizing the logistic loss directly, the proposed method minimizes the logistic loss by minimizing an auxiliary function that provides an upper-bound of the logistic loss.
This approach is generally called the majorization-maximization (MM) algorithm \cite{sun2016majorization}.
It also can be called as an Expectation-Maximization (EM) algorithm because it employs the P\'{o}lya-Gamma (PG) augmentation \cite{polson2013bayesian,scott2013expectation} to derive the auxiliary function.
The EM algorithm based on the PG augmentation introduces a latent variable that follows the PG distribution, and the auxiliary function of the loss function can be derived by calculating the expected value of the latent variable.
The auxiliary function is given in the form of a weighted squared loss and can be optimized by ALS.
The monotonically decreasing (non-increasing) logistic loss is guaranteed due to the nature of MM and ALS. 

\begin{figure}[t]
\centering
\includegraphics[width=0.8\textwidth]{fig001/optimization_behavior_l2.eps}
\caption{Comparison of algorithms in TN least squares regression (TNLSR)}\label{fig:opt_l2}
\end{figure}

\section{Derivation of auxiliary function by PG augmentation}
\subsection{Theoretical tools for PG augmentation}
The P\'{o}lya-Gamma (PG) augmentation~\cite{polson2013bayesian} is a method proposed for Bayesian statistical modeling in logistic regression and generalized linear models. 

The random variable $\omega$ of a PG distribution has parameters $b>0$ and $c\in{\mathbb R}$, and $\omega\sim\text{PG}(b,c)$ is expressed as a weighted sum of an infinite number of gamma random numbers $g_k \sim \text{Ga}(b,1)$ 
\begin{equation}
\omega{\overset{d}{=}}\frac{1}{2\pi^2}\sum_{k=1}^{\infty}{\frac{g_k}{(k-\frac{1}{2})^2+\frac{c^2}{4\pi^2}}}, \label{eq:PG_dist}
\end{equation}
where $\overset{d}{=}$ is the equality of the distributions. The expected value of $\omega\sim\text{PG}(b,c)$ is given by 
 \begin{equation}
{\mathbb E}[\omega]=\frac{b}{2c}\tanh(\frac{c}{2}) \label{eq:PG_expectation}.
\end{equation}
The probability density function of the PG distribution has the form 
\begin{equation}
p(\omega{\verb+|+}b,c)=\frac{e^{-\frac{c^2}{2}\omega}p(\omega|b,0)}{{\mathbb E}_{\omega{\sim}\text{PG}(1,0)}{\{}e^{-\frac{c^2}{2}\omega}\}}. \label{eq:PG_theorem}
\end{equation}
Polson et al. proved a useful property of the variable $\omega$ 
\begin{equation}
\frac{(e^\delta)^a}{(1+e^\delta)^b}=2^{-b}e^{{\kappa}\delta}\int_0^{\infty}{e^{{-\omega}\frac{\delta^2}{2}}p(\omega)d\omega},\label{eq:PG_logistic_to_gauss}
\end{equation}
where $\kappa=a-\frac{b}{2}$ and $p(\omega)=\text{PG}(\omega{\verb+|+}b,0)$. It is important to note that (\ref{eq:PG_logistic_to_gauss}) expresses a logistic function in the form of an integral of a Gaussian function. 

\subsection{Derivation of auxiliary function}
The $n$-th contribution of the likelihood function in logistic regression is given by 
\begin{equation}
L_n =\frac{{(e^{\psi(\mathbf x_n)})}^{y_n}}{1+e^{\psi(\mathbf x_n)}}.
\end{equation}
The logistic loss in equation (\ref{eq:logistic_loss}) is a negative log-likelihood, then we have $-\log L_n = l_n$. Applying Eq. (\ref{eq:PG_logistic_to_gauss}), we have 
\begin{equation}
L_n(\psi_n)=\frac{1}{2}{e^{{\kappa_n}\psi_n}}\int^{\infty}_{0}{e^{-\omega_n\frac{\psi_n^2}{2}}p(\omega_n|1,0)d\omega_n}.
\end{equation}
For simplicity, we put $\psi_n = \psi(\mathbf x_n)$ and $\kappa_n = y_n - \frac{1}{2}$.
From the properties of the PG distribution (\ref{eq:PG_theorem}) and Jensen's inequality, we can derive a function for the likelihood function as 
\begin{equation}
L'_n(\psi_n|\psi'_n) = \frac{\alpha_n}{2} e^{\kappa_n\psi_n}e^{-\frac{\psi_n^2-\psi'^{2}_n}{2}\omega'_n}. \label{eq:aux_func}
\end{equation}
Note that $\alpha_n = {\mathbb E}_{\omega_n{\sim}\text{PG}(1,0)}{\{}e^{-\omega_n{\frac{\psi'^{2}_n}{2}}}\}$ and $\omega'_n={\mathbb E}_{\omega_n{\sim}\text{PG}(1,\psi'_n)}[\omega_n]$. Clearly we have
\begin{align}
  L_n'(\psi_n | \psi_n') \leq L_n(\psi_n),  L_n'(\psi_n | \psi_n) = L_n(\psi_n).
\end{align}
Since this function $L_n'$ is lower bounded with respect to the likelihood function $L_n$, its negative logarithm is an upper-bound function with respect to the logistic loss. 

\begin{figure}[t]
\centering
\includegraphics[width=0.7\textwidth]{fig001/PG_based_majorization_of_logistic_loss.eps}
\caption{Logistic loss (negative log-likelihood) and its auxiliary function}\label{fig:upper_bound}
\end{figure}

From the negative logarithm of the function $L_n'$, the auxiliary function can be derived as 
\begin{align}
 l_n'(\psi_n|\psi'_n) = \frac{1}{2}\omega'_n(\psi_n-\frac{\kappa_n}{\omega'_n})^2+C_n\label{eq:upperbound}%.
\end{align}
where $C_n$ is a constant.
It is theoretically clear that $l_n'(\psi_n|\psi'_n)$ is an upper-bound function of $l_n(\psi_n)$, which can also be confirmed from Figure \ref{fig:upper_bound}.
Let $\psi_n = \psi(\mathbf x_n | \theta) $, $\theta = \{\tensor{A}_1, ..., \tensor{A}_M, v\}$, the overall auxiliary function is given by 
\begin{align}
g(\theta | \theta')&=\frac{1}{2}\sum_{n \in [N]}{\omega'_n \left(\psi(\mathbf x_n | \theta)-\frac{\kappa_n}{\omega'_n}\right)^2} + C,\label{eq:sum_of_WLS} \\
\omega_n' &= \frac{1}{2 \psi(\mathbf x_n | \theta')} \tanh{ \frac{\psi(\mathbf x_n | \theta')}{2} },
\end{align}
where $C$ is a constant term that does not contribute to optimization. The expected value of the latent variable $\omega_n'$ is then computed from the inference result based on the current coefficient values $\theta'$ , and the procedure for solving the least-squares method (EM algorithm) is derived using the obtained $\omega_n'$ as weight coefficients.
If we put $\omega_n' = 1$ for all $n$, it reduces to least squares regression. From this perspective, this is a kind of generalization of TNLSR.

\section{TN Logistic Regression}
\subsection{EM algorithm}
Letting $h(\theta)$ denote the logistic loss, we have 
\begin{align}
h(\theta) \leq g(\theta|\theta'), \ \ \ h(\theta) = g(\theta|\theta).
\end{align}
From this property, the algorithm 
\begin{align}
 \theta^{(t+1)} = \argmin_\theta g(\theta | \theta^{(t)})
\end{align}
guarantees that $h(\theta)$ is monotonically decreasing:
\begin{align}
h(\theta^{(t+1)}) \leq g(\theta^{(t+1)} | \theta^{(t)}) \leq g(\theta^{(t)} | \theta^{(t)}) = h(\theta^{(t)}).
\end{align}

The specific procedure in TNLR is a bit more complicated and is given as in Algorithm~\ref{alg:TNEM}.
It consists of a combination of ALS with $M$ blocks of $\theta$, orthogonalization using QR decomposition for each update, forward sweep, and backward sweep.
Figure \ref{fig:fsweep} shows an image of the forward sweep for reference. The orthogonalized core tensor is represented by a node with diagonal lines. The upper triangular matrix obtained by QR decomposition is integrated with the next core tensor. Then its orthogonalization can be achieved without changing the value of the cost function. 

\begin{algorithm}[t]
\caption{EM algorithm for TN logistic regression}\label{alg:TNEM}
\begin{algorithmic}[1]
  \STATE Initialize $\theta = \{\tensor{A}_1, \tensor{A}_2, ..., \tensor{A}_M, v\}$ 
  \REPEAT
  \STATE (Forward Sweep)
  \FOR{$m = 1, 2, ..., M-1$}
    \STATE Calculate $\omega_n$ from the current $\theta$;
    \STATE $(\tensor{A}_m, v) \leftarrow \argmin_{(\tensor{A}_m,v)} g(\tensor{A}_m, v | \theta)$;
    \STATE Orthogonalize $\tensor{A}_m$ by QR decomposition and integrate upper triangular matrix into $\tensor{A}_{m+1}$;
    \STATE Overwrite $\theta = \{\tensor{A}_1, \tensor{A}_2, ..., \tensor{A}_M, v\}$ with the latest parameters;
  \ENDFOR
  \STATE (Backward Sweep)  
  \FOR{$m = M, M-1, ..., 2$}
    \STATE Calculate $\omega_n$ from the current $\theta$;
    \STATE $(\tensor{A}_m, v) \leftarrow \argmin_{(\tensor{A}_m,v)} g(\tensor{A}_m, v | \theta)$;
    \STATE Orthogonalize $\tensor{A}_m$ by QR decomposition and integrate the upper triangular matrix into $\tensor{A}_{m-1}$;
    \STATE Overwrite $\theta = \{\tensor{A}_1, \tensor{A}_2, ..., \tensor{A}_M, v\}$ with the latest parameters;
  \ENDFOR 
  \UNTIL{Convergence}
\end{algorithmic}
\end{algorithm}


\begin{figure}[t]
\begin{center}
\includegraphics[width=0.74\textwidth]{fig001/45.pdf}
\caption{Forward sweep}\label{fig:fsweep}
\end{center}
\end{figure}

\subsection{Update core tensors by solving sub-optimization problems}
This section explains the update of the $m$-th core tensor $\boldsymbol{\mathcal{A}}_m$ and bias $v$. We consider the following sub-optimization problems:
\begin{align}
\underset{\boldsymbol{\mathcal{A}}_m, v}{\text{minimize}} & \ \ g(\boldsymbol{\mathcal{A}}_m, v | 
 \theta^{(t)}).
\end{align}
The update formula is the same for both forward and backward sweeps.
First, focusing on the coefficient $\boldsymbol{\mathcal{A}}_m$, the inner product part of the regression model can be transformed as (Figure \ref{fig:TN_sub})
\begin{align}
\langle\mathbf\Phi(\mathbf x_n),\boldsymbol{\mathcal{W}}\rangle=\langle{\mathbf l_n^{(m)}\otimes\bm\phi_n^{ (m)}\otimes{\mathbf r_n^{(m)}},\boldsymbol{\mathcal{A}}_m}\rangle.
\end{align}
Note that, by setting the optimization parameter as $\bm\beta = [\text{vec}(\tensor{A}_m)^\top, v]^\top $, the solution for $\bm\beta$ of the auxiliary function (\ref{eq:sum_of_WLS}) is 
\begin{align}
  \hat{\bm\beta} = (\mathbf{Z}^\top \mathbf\Omega \mathbf{Z})^{-1} \mathbf{Z}^\top \bm\kappa,
\end{align}
where we put
\begin{align}
& \bm\kappa = \left[ \kappa_1, \kappa_2, ..., \kappa_N \right]^\top, \\
& \mathbf\Omega = \text{diag}(\omega_1', \omega_2', ..., \omega_N'), \\
& \mathbf Z = \begin{bmatrix}
 (\mathbf l_1^{(m)}\otimes\bm\phi_1^{ (m)}\otimes{\mathbf r_1^{(m)}})^\top & 1 \\
 (\mathbf l_2^{(m)}\otimes\bm\phi_2^{ (m)}\otimes{\mathbf r_2^{(m)}})^\top & 1 \\
 \vdots & \vdots \\
 (\mathbf l_N^{(m)}\otimes\bm\phi_N^{ (m)}\otimes{\mathbf r_N^{(m)}})^\top & 1 \end{bmatrix}.
\end{align}
If the matrix $ \mathbf{Z}^\top \mathbf\Omega \mathbf{Z} $ is not regular, we can use the Tikonov regularization $\hat{\bm\beta} = (\mathbf{Z}^\top \mathbf\Omega \mathbf{Z} + \epsilon \mathbf I )^{-1} \mathbf{Z}^\top \bm\kappa$ with positive hyper-parameter $\epsilon > 0$.

\begin{figure}[t]
\begin{center}
\includegraphics[width=0.5\textwidth]{fig001/4_4.pdf}
\caption{Variants of regression models}\label{fig:TN_sub}
\end{center}
\end{figure}

\section{Experimental Results}
In this experiment, we evaluate the effectiveness of the algorithm as an optimization algorithm and as a classifier.
Experiments were conducted on the subject of learning classifier in MNIST and Fashion MNIST.
Since the objective is to perform binary classification using logistic regression, the data was divided into even and odd labels, which were used as virtual data for the binary classification.
We downsampled an image of size (28,28) to (7,7) and normalized the pixel values to [0,1]. 49-dimensional vector $\bm x \in [0,1]^{49}$ was used as the input feature.
\subsection{Optimization algorithm}
Figure \ref{fig:opt_behav} shows how the logistic loss changes for each value of the bond dimension $R$.
The number of iterations refers to the number of times a partial update of $(\tensor{A}_m,v)$ is performed (i.e., 48*2=96 times for 1 sweep). 
The logistic loss is shown in Figure 6.
From Figure \ref{fig:opt_behav}, it can be seen that the logistic loss is monotonically decreasing.
As $R$ increases, the learning progresses more quickly and the optimal value obtained is lower.

\begin{figure}[t]
\centering
\includegraphics[width=0.7\textwidth]{fig001/rank_loss.eps}
\caption{Behavior of MNIST learning process}\label{fig:opt_behav}
\end{figure}

\subsection{Evaluation as a classifier}
Figure \ref{fig:acc_behav} shows the classification accuracy results for the MNIST classification task for each value of the bond dimension $R$.
Logistic regression outperformed least-squares regression in fitting the training data, reflecting the advantage of employing logistic loss.
On the other hand, the accuracy of the test data showed no significant difference in generalization performance.
In a comparison including convolutional neural networks (CNN), the classification accuracy was lower than that of CNN (Table \ref{tab:comp}). 

\begin{figure}[t]
\centering
\includegraphics[width=0.7\textwidth]{fig001/accuracyrank.eps}
\caption{Behavior of MNIST classification accuracy}\label{fig:acc_behav}
\end{figure}

\begin{table}
\caption{Comparison of classification accuracy.}\label{tab:comp}
\centering
\begin{tabular}{|c|c|c|}
\hline
 & MNIST & F-MNIST \\
\hline
TNLSR (R=32) & 98.5 & 97.8    \\ \hline
TNLR (R=32) & 98.2 & 97.5    \\ \hline
CNN         & 99.1 & 99.2    \\ \hline
\end{tabular}
\end{table}

\section{Conclusion}
In this study, we proposed an EM algorithm for learning TN by logistic loss.
Unlike the gradient method, the learning rate does not need to be adjusted and the objective function is monotonically decreasing, which are major advantages.
The future direction of TN regression is to develop an algorithm that can cope with softmax and cross-entropy loss.
It is also important to explore the potential of TN regression models by searching for embedding methods and TN structures that are more suitable for image classification.


\input{references001}
%\end{document}
