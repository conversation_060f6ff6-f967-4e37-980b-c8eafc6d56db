%%%%%%%%%%%%%%%%%%%%%%%% referenc.tex %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% sample references
% %
% Use this file as a template for your own input.
%
%%%%%%%%%%%%%%%%%%%%%%%% Springer-Verlag %%%%%%%%%%%%%%%%%%%%%%%%%%
%
% BibTeX users please use
% \bibliographystyle{}
% \bibliography{}
%
%\biblstarthook{}

\begin{thebibliography}{99.}%
% and use \bibitem to create references.
%
% Use the following syntax and markup for your references if 
% the subject of your book is from the field 
% "Mathematics, Physics, Statistics, Computer Science"
%
% Contribution 
\bibitem{bailey2023image}
L.~Bailey, E.~<PERSON>g, <PERSON>.~<PERSON>, and <PERSON><PERSON>~<PERSON>, ``Image hijacks: Adversarial
  images can control generative models at runtime,'' \emph{arXiv preprint
  arXiv:2309.00236}, 2023.

\bibitem{qi2024visual}
X.~<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>~<PERSON>, ``Visual
  adversarial examples jailbreak aligned large language models,'' in
  \emph{Proceedings of the AAAI Conference on Artificial Intelligence},
  vol.~38, no.~19, 2024, pp. 21\,527--21\,536.

\bibitem{gong2023figstep}
Y.~Gong, D.~Ran, J.~Liu, C.~Wang, T.~Cong, A.~Wang, S.~Duan, and X.~Wang,
  ``Figstep: Jailbreaking large vision-language models via typographic visual
  prompts,'' \emph{arXiv preprint arXiv:2311.05608}, 2023.

\bibitem{wang2024transferable}
H.~Wang, K.~Dong, Z.~Zhu, H.~Qin, A.~Liu, X.~Fang, J.~Wang, and X.~Liu,
  ``Transferable multimodal attack on vision-language pre-training models,'' in
  \emph{2024 IEEE Symposium on Security and Privacy (SP)}.\hskip 1em plus 0.5em
  minus 0.4em\relax IEEE Computer Society, 2024, pp. 102--102.

\bibitem{liu2023autodan}
X.~Liu, N.~Xu, M.~Chen, and C.~Xiao, ``Autodan: Generating stealthy jailbreak
  prompts on aligned large language models,'' \emph{arXiv preprint
  arXiv:2310.04451}, 2023.
\end{thebibliography}
