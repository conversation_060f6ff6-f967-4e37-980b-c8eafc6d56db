
@article{naumov2019deep,
  title={Deep learning recommendation model for personalization and recommendation systems},
  author={<PERSON><PERSON><PERSON>, <PERSON> and Mu<PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON><PERSON> and others},
  journal={arXiv preprint arXiv:1906.00091},
  year={2019}
}


@incollection{gholami2022survey,
  title={A survey of quantization methods for efficient neural network inference},
  author={<PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON>},
  booktitle={Low-Power Computer Vision},
  pages={291--326},
  year={2022},
  address   = {Boca Raton, FL, USA},
  publisher={Chapman and Hall/CRC}
}

@inproceedings{jain2020checkmate,
  title={Checkmate: Breaking the Memory Wall with Optimal Tensor Rematerialization},
  author={<PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, <PERSON> and <PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON>},
  booktitle={Proceedings of Machine Learning and Systems},
  volume={2},
  pages={497--511},
  year={2020}
}

@article{zhao2020distributed,
  title={Distributed hierarchical gpu parameter server for massive scale deep learning ads systems},
  author={Zhao, Weijie and Xie, Deping and Jia, Ronglai and Qian, Yulei and Ding, Ruiquan and Sun, Mingming and Li, Ping},
  journal={Proceedings of Machine Learning and Systems},
  volume={2},
  pages={412--428},
  year={2020}
}

@article{ishkhanov2020time,
  title={Time-based sequence model for personalization and recommendation systems},
  author={Ishkhanov, Tigran and Naumov, Maxim and Chen, Xianjie and Zhu, Yan and Zhong, Yuan and Azzolini, Alisson Gusatti and Sun, Chonglin and Jiang, Frank and Malevich, Andrey and Xiong, Liang},
  journal={arXiv preprint arXiv:2008.11922},
  year={2020}
}

@article{guan2019post,
  title={Post-training 4-bit quantization on embedding tables},
  author={Guan, Hui and Malevich, Andrey and Yang, Jiyan and Park, Jongsoo and Yuen, Hector},
  journal={arXiv preprint arXiv:1911.02079},
  year={2019}
}

@article{osti_10463062,
place = {Country unknown/Code not available}, title = {The trade-offs of model size in large recommendation models : A 10000x compressed criteo-TB DLRM model (100 GB parameters to mere 10MB)}, url = {https://par.nsf.gov/biblio/10463062}, abstractNote = {}, journal = {Advances in Neural Information Processing Systems 2022 (NeurIPS 2022)}, author = {Desai, Aditya and Shrivastava, Anshumali}, 
year = {2022}}

@article{yin2021tt,
  title={Tt-rec: Tensor train compression for deep learning recommendation models},
  author={Yin, Chunxing and Acun, Bilge and Wu, Carole-Jean and Liu, Xing},
  journal={Proceedings of Machine Learning and Systems},
  volume={3},
  pages={448--462},
  year={2021}
}

@inproceedings{zhang2023shark,
  title={SHARK: A Lightweight Model Compression Approach for Large-scale Recommender Systems},
  author={Zhang, Beichuan and Sun, Chenggen and Tan, Jianchao and Cai, Xinjun and Zhao, Jun and Miao, Mengqi and Yin, Kang and Song, Chengru and Mou, Na and Song, Yang},
  booktitle={Proceedings of the 32nd ACM International Conference on Information and Knowledge Management},
  pages={4930--4937},
  year={2023}
}

@inproceedings{mudigere2022software,
  title={Software-hardware co-design for fast and scalable training of deep learning recommendation models},
  author={Mudigere, Dheevatsa and Hao, Yuchen and Huang, Jianyu and Jia, Zhihao and Tulloch, Andrew and Sridharan, Srinivas and Liu, Xing and Ozdal, Mustafa and Nie, Jade and Park, Jongsoo and others},
  booktitle={Proceedings of the 49th Annual International Symposium on Computer Architecture},
  pages={993--1011},
  year={2022}
}


@article{li2024embedding,
  title={Embedding Compression in Recommender Systems: A Survey},
  author={Li, Shiwei and Guo, Huifeng and Tang, Xing and Tang, Ruiming and Hou, Lu and Li, Ruixuan and Zhang, Rui},
  journal={ACM Computing Surveys},
  volume={56},
  number={5},
  pages={1--21},
  year={2024},
  publisher={ACM New York, NY}
}

@inproceedings{shi2020compositional,
  title={Compositional embeddings using complementary partitions for memory-efficient recommendation systems},
  author={Shi, Hao-Jun Michael and Mudigere, Dheevatsa and Naumov, Maxim and Yang, Jiyan},
  booktitle={Proceedings of the 26th ACM SIGKDD International Conference on Knowledge Discovery \& Data Mining},
  pages={165--175},
  year={2020}
}

@inproceedings{ginart2021mixed,
  title={Mixed dimension embeddings with application to memory-efficient recommendation systems},
  author={Ginart, Antonio A and Naumov, Maxim and Mudigere, Dheevatsa and Yang, Jiyan and Zou, James},
  booktitle={2021 IEEE International Symposium on Information Theory (ISIT)},
  pages={2786--2791},
  year={2021},
  organization={IEEE}
}

@article{pansare2022learning,
  title={Learning compressed embeddings for on-device inference},
  author={Pansare, Niketan and Katukuri, Jay and Arora, Aditya and Cipollone, Frank and Shaik, Riyaaz and Tokgozoglu, Noyan and Venkataraman, Chandru},
  journal={Proceedings of Machine Learning and Systems},
  volume={4},
  pages={382--397},
  year={2022}
}

@inproceedings{gupta2020architectural,
  title={The architectural implications of facebook's dnn-based personalized recommendation},
  author={Gupta, Udit and Wu, Carole-Jean and Wang, Xiaodong and Naumov, Maxim and Reagen, Brandon and Brooks, David and Cottel, Bradford and Hazelwood, Kim and Hempstead, Mark and Jia, Bill and others},
  booktitle={2020 IEEE International Symposium on High Performance Computer Architecture (HPCA)},
  pages={488--501},
  year={2020},
  organization={IEEE}
}

@inproceedings{agarwal2023bagpipe,
  title={Bagpipe: Accelerating deep recommendation model training},
  author={Agarwal, Saurabh and Yan, Chengpo and Zhang, Ziyi and Venkataraman, Shivaram},
  booktitle={Proceedings of the 29th Symposium on Operating Systems Principles},
  pages={348--363},
  year={2023}
}

@article{desai2022random,
  title={Random Offset Block Embedding (ROBE) for compressed embedding tables in deep learning recommendation systems},
  author={Desai, Aditya and Chou, Li and Shrivastava, Anshumali},
  journal={Proceedings of Machine Learning and Systems},
  volume={4},
  pages={762--778},
  year={2022}
}

@inproceedings{rashidi2021enabling,
  title={Enabling compute-communication overlap in distributed deep learning training platforms},
  author={Rashidi, Saeed and Denton, Matthew and Sridharan, Srinivas and Srinivasan, Sudarshan and Suresh, Amoghavarsha and Nie, Jade and Krishna, Tushar},
  booktitle={2021 ACM/IEEE 48th Annual International Symposium on Computer Architecture (ISCA)},
  pages={540--553},
  year={2021},
  organization={IEEE}
}

@article{zhou2024dqrm,
  title={DQRM: Deep Quantized Recommendation Models},
  author={Zhou, Yang and Dong, Zhen and Chan, Ellick and Kalamkar, Dhiraj and Marculescu, Diana and Keutzer, Kurt},
  journal={arXiv preprint arXiv:2410.20046},
  year={2024}
}

@inproceedings{cheng2016wide,
  title={Wide \& deep learning for recommender systems},
  author={Cheng, Heng-Tze and Koc, Levent and Harmsen, Jeremiah and Shaked, Tal and Chandra, Tushar and Aradhye, Hrishi and Anderson, Glen and Corrado, Greg and Chai, Wei and Ispir, Mustafa and others},
  booktitle={Proceedings of the 1st workshop on deep learning for recommender systems},
  pages={7--10},
  year={2016}
}

@article{zhao2023pytorch,
  title={Pytorch fsdp: experiences on scaling fully sharded data parallel},
  author={Zhao, Yanli and Gu, Andrew and Varma, Rohan and Luo, Liang and Huang, Chien-Chin and Xu, Min and Wright, Less and Shojanazeri, Hamid and Ott, Myle and Shleifer, Sam and others},
  journal={arXiv preprint arXiv:2304.11277},
  year={2023}
}

@inproceedings{dong2019hawq,
  title={Hawq: Hessian aware quantization of neural networks with mixed-precision},
  author={Dong, Zhen and Yao, Zhewei and Gholami, Amir and Mahoney, Michael W and Keutzer, Kurt},
  booktitle={Proceedings of the IEEE/CVF international conference on computer vision},
  pages={293--302},
  year={2019}
}

@article{zhao2022analysis,
  title={Analysis of quantization on mlp-based vision models},
  author={Zhao, Lingran and Dong, Zhen and Keutzer, Kurt},
  journal={arXiv preprint arXiv:2209.06383},
  year={2022}
}

@inproceedings{mangalam2022reversible,
  title={Reversible vision transformers},
  author={Mangalam, Karttikeya and Fan, Haoqi and Li, Yanghao and Wu, Chao-Yuan and Xiong, Bo and Feichtenhofer, Christoph and Malik, Jitendra},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={10830--10840},
  year={2022}
}

@article{jia2019beyond,
  title={Beyond data and model parallelism for deep neural networks.},
  author={Jia, Zhihao and Zaharia, Matei and Aiken, Alex},
  journal={Proceedings of Machine Learning and Systems},
  volume={1},
  pages={1--13},
  year={2019}
}

@inproceedings{liu2023noisyquant,
  title={Noisyquant: Noisy bias-enhanced post-training activation quantization for vision transformers},
  author={Liu, Yijiang and Yang, Huanrui and Dong, Zhen and Keutzer, Kurt and Du, Li and Zhang, Shanghang},
  booktitle={Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages={20321--20330},
  year={2023}
}

@article{shang2023pb,
  title={Pb-llm: Partially binarized large language models},
  author={Shang, Yuzhang and Yuan, Zhihang and Wu, Qiang and Dong, Zhen},
  journal={arXiv preprint arXiv:2310.00034},
  year={2023}
}

@article{li2023qft,
  title={QFT: Quantized Full-parameter Tuning of LLMs with Affordable Resources},
  author={Li, Zhikai and Liu, Xiaoxuan and Zhu, Banghua and Dong, Zhen and Gu, Qingyi and Keutzer, Kurt},
  journal={arXiv preprint arXiv:2310.07147},
  year={2023}
}

@article{shoeybi2019megatron,
  title={Megatron-lm: Training multi-billion parameter language models using model parallelism},
  author={Shoeybi, Mohammad and Patwary, Mostofa and Puri, Raul and LeGresley, Patrick and Casper, Jared and Catanzaro, Bryan},
  journal={arXiv preprint arXiv:1909.08053},
  year={2019}
}
