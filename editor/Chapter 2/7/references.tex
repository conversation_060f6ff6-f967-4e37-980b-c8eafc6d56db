%%%%%%%%%%%%%%%%%%%%%%%% referenc.tex %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% sample references
% %
% Use this file as a template for your own input.
%
%%%%%%%%%%%%%%%%%%%%%%%% Springer-Verlag %%%%%%%%%%%%%%%%%%%%%%%%%%
%
% BibTeX users please use
% \bibliographystyle{}
% \bibliography{}
%
\begin{thebibliography}{99.}%
% and use \bibitem to create references.
%
% Use the following syntax and markup for your references if 
% the subject of your book is from the field 
% "Mathematics, Physics, Statistics, Computer Science"
%

\bibitem{b2} <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. "Deep learning." nature 521, no. 7553 (2015): 436-444.


\bibitem{b4} <PERSON>, <PERSON>, <PERSON>, and <PERSON>. "Fully convolutional networks for semantic segmentation." In Proceedings of the IEEE conference on computer vision and pattern recognition, pp. 3431-3440. 2015.


\bibitem{b6} <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. "Explaining and harnessing adversarial examples." arXiv preprint arXiv:1412.6572 (2014).


\bibitem{b5} Taylor, Luke, and Geoff Nitschke. "Improving deep learning with generic data augmentation." In 2018 IEEE symposium series on computational intelligence (SSCI), pp. 1542-1547. IEEE, 2018.

\bibitem{b1} Cohen, Taco, and Max Welling. "Group equivariant convolutional networks." In International conference on machine learning, pp. 2990-2999. PMLR, 2016.



\bibitem{b3} He, Kaiming, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. "Spatial pyramid pooling in deep convolutional networks for visual recognition." IEEE transactions on pattern analysis and machine intelligence 37, no. 9 (2015): 1904-1916.
\end{thebibliography}
